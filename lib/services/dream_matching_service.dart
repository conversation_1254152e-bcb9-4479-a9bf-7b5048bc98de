import 'dart:math';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../app/models/dream_match.dart';

class DreamMatchingService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  /// Find similar dreams for a given dream
  static Future<List<DreamMatch>> findSimilarDreams(
    String dreamId,
    Map<String, dynamic> dreamData,
  ) async {
    try {
      // Get all other dreams from last 7 days (excluding current user's dreams for privacy)
      final currentUserId = dreamData['userId'];
      final oneWeekAgo = DateTime.now().subtract(const Duration(days: 7));
      final oneWeekAgoTimestamp = Timestamp.fromDate(oneWeekAgo);

      final allDreamsQuery = await _firestore
          .collection('dreams')
          .where('userId', isNotEqualTo: currentUserId)
          .where('date', isGreaterThanOrEqualTo: oneWeekAgoTimestamp)
          .get(const GetOptions(source: Source.server))
          .timeout(
            const Duration(seconds: 15),
            onTimeout: () {
              print('Network timeout, trying cache...');
              return _firestore
                  .collection('dreams')
                  .where('userId', isNotEqualTo: currentUserId)
                  .where('date', isGreaterThanOrEqualTo: oneWeekAgoTimestamp)
                  .get(const GetOptions(source: Source.cache));
            },
          );

      final List<DreamMatch> matches = [];

      for (final doc in allDreamsQuery.docs) {
        final otherDreamData = doc.data();

        // Clean dream data for comparison (remove AI interpretation)
        final cleanDreamData = _cleanDreamDataForComparison(dreamData);
        final cleanOtherDreamData = _cleanDreamDataForComparison(otherDreamData);

        // Calculate similarity score using cleaned data
        final similarity = await _calculateSimilarity(cleanDreamData, cleanOtherDreamData);

        // Debug: Print detailed similarity breakdown
        final textSim = _calculateTextSimilarity(
          cleanDreamData['description'] ?? '',
          cleanOtherDreamData['description'] ?? '',
        );
        final tagSim = _calculateTagSimilarity(
          List<String>.from(cleanDreamData['tags'] ?? []),
          List<String>.from(cleanOtherDreamData['tags'] ?? []),
        );
        final typeSim = _calculateTypeSimilarity(
          cleanDreamData['dreamType'] ?? '',
          cleanOtherDreamData['dreamType'] ?? '',
        );
        final sleepSim = _calculateSleepQualitySimilarity(
          cleanDreamData['sleepQuality']?.toDouble() ?? 50.0,
          cleanOtherDreamData['sleepQuality']?.toDouble() ?? 50.0,
        );

        final actualPercentage = (similarity * 100).round();
        print('=== Dream Matching Analysis ===');
        print('Comparing with Dream ${doc.id}:');
        print('  Title: "${otherDreamData['title'] ?? 'No title'}"');
        print('  Description length: ${(otherDreamData['description'] ?? '').length} chars');
        print('  Tags: ${otherDreamData['tags'] ?? []}');
        print('  Type: ${otherDreamData['dreamType'] ?? 'Unknown'}');
        print('  Text: ${(textSim * 100).round()}% (weight: 60%)');
        print('  Tags: ${(tagSim * 100).round()}% (weight: 25%)');
        print('  Type: ${(typeSim * 100).round()}% (weight: 10%)');
        print('  Sleep: ${(sleepSim * 100).round()}% (weight: 5%)');
        print('  Total: $actualPercentage% (threshold: 50%)');
        print('  Match: ${actualPercentage >= 50 ? "YES" : "NO"}');
        print('  Display: ${actualPercentage >= 50 && actualPercentage < 70 ? "70%" : "$actualPercentage%"}');
        print('================================');

        // Include dreams with >50% similarity (but display minimum 70%)
        if (similarity >= 0.50) {
          matches.add(DreamMatch(
            dreamId: doc.id,
            userId: otherDreamData['userId'] ?? '',
            dreamData: otherDreamData,
            similarityScore: similarity,
          ));
        }
      }

      // Sort by similarity score (highest first)
      matches.sort((a, b) => b.similarityScore.compareTo(a.similarityScore));

      return matches;
    } catch (e) {
      print('Error finding similar dreams: $e');
      return [];
    }
  }

  /// Clean dream data for comparison by removing processing fields and metadata
  static Map<String, dynamic> _cleanDreamDataForComparison(Map<String, dynamic> dreamData) {
    final cleanData = Map<String, dynamic>.from(dreamData);

    // Remove processing fields and metadata that shouldn't affect similarity
    cleanData.remove('matchingProcessed');
    cleanData.remove('matchingProcessedAt');
    cleanData.remove('matchingStatus');
    cleanData.remove('matchingStartedAt');
    cleanData.remove('matchingCompletedAt');
    cleanData.remove('matchingError');
    cleanData.remove('matchingFailedAt');
    cleanData.remove('similarDreamsCount');
    cleanData.remove('createdAt');
    cleanData.remove('updatedAt');
    cleanData.remove('date'); // Rüya tarihi benzerlik hesaplamasına katılmamalı
    cleanData.remove('userId'); // Kullanıcı ID'si benzerlik hesaplamasına katılmamalı

    return cleanData;
  }

  /// Calculate similarity between two dreams
  static Future<double> _calculateSimilarity(
    Map<String, dynamic> dream1,
    Map<String, dynamic> dream2,
  ) async {
    // Text similarity (60% weight)
    final textSimilarity = _calculateTextSimilarity(
      dream1['description'] ?? '',
      dream2['description'] ?? '',
    );

    // Tag similarity (25% weight)
    final tagSimilarity = _calculateTagSimilarity(
      List<String>.from(dream1['tags'] ?? []),
      List<String>.from(dream2['tags'] ?? []),
    );

    // Dream type similarity (10% weight)
    final typeSimilarity = _calculateTypeSimilarity(
      dream1['dreamType'] ?? '',
      dream2['dreamType'] ?? '',
    );

    // Sleep quality similarity (5% weight)
    final sleepSimilarity = _calculateSleepQualitySimilarity(
      dream1['sleepQuality']?.toDouble() ?? 50.0,
      dream2['sleepQuality']?.toDouble() ?? 50.0,
    );

    // Weighted total
    final totalSimilarity = (textSimilarity * 0.60) +
        (tagSimilarity * 0.25) +
        (typeSimilarity * 0.10) +
        (sleepSimilarity * 0.05);

    return totalSimilarity;
  }

  /// Calculate text similarity using enhanced semantic analysis with fuzzy matching
  static double _calculateTextSimilarity(String text1, String text2) {
    if (text1.isEmpty || text2.isEmpty) return 0.0;

    // If texts are identical, return 1.0
    if (text1.toLowerCase().trim() == text2.toLowerCase().trim()) return 1.0;

    // Clean and normalize texts
    final cleanText1 = _normalizeText(text1);
    final cleanText2 = _normalizeText(text2);

    // Multi-level similarity analysis with improved weights
    final scores = <double>[];

    // 1. Enhanced keyword-based similarity with fuzzy matching (50% weight)
    final keywordSim = _calculateEnhancedKeywordSimilarity(cleanText1, cleanText2);
    scores.add(keywordSim * 0.50);

    // 2. Fuzzy string similarity for overall text (30% weight)
    final fuzzySim = _calculateFuzzyStringSimilarity(cleanText1, cleanText2);
    scores.add(fuzzySim * 0.30);

    // 3. Semantic concept similarity (20% weight)
    final conceptSim = _calculateConceptSimilarity(cleanText1, cleanText2);
    scores.add(conceptSim * 0.20);

    final totalSimilarity = scores.reduce((a, b) => a + b);

    // Debug output for text similarity
    print('    Text similarity breakdown:');
    print('      Enhanced Keywords: ${(keywordSim * 100).round()}%');
    print('      Fuzzy String: ${(fuzzySim * 100).round()}%');
    print('      Concepts: ${(conceptSim * 100).round()}%');
    print('      Total Text: ${(totalSimilarity * 100).round()}%');

    return totalSimilarity;
  }

  /// Normalize text for better comparison
  static String _normalizeText(String text) {
    return text
        .toLowerCase()
        .replaceAll(RegExp(r'[^\w\s]'), ' ')
        .replaceAll(RegExp(r'\s+'), ' ')
        .trim();
  }

  /// Tokenize text into words
  static List<String> _tokenize(String text) {
    return text
        .replaceAll(RegExp(r'[^\w\s]'), ' ')
        .split(RegExp(r'\s+'))
        .where((word) => word.length > 1) // Filter only single characters
        .toList();
  }

  /// Enhanced keyword-based similarity with fuzzy matching and cross-language support
  static double _calculateEnhancedKeywordSimilarity(String text1, String text2) {
    final tokens1 = _tokenize(text1);
    final tokens2 = _tokenize(text2);

    if (tokens1.isEmpty || tokens2.isEmpty) return 0.0;

    // Dream keyword mappings (Turkish-English pairs and variations)
    final keywordMappings = {
      // Flying/Movement
      'uçmak': ['flying', 'fly', 'uçuyordum', 'uçtum'],
      'flying': ['uçmak', 'uçuyordum', 'uçtum', 'fly'],
      'düşmek': ['falling', 'fall', 'düştüm', 'düşüyordum'],
      'falling': ['düşmek', 'düştüm', 'düşüyordum', 'fall'],
      'koşmak': ['running', 'run', 'koştum', 'koşuyordum'],
      'running': ['koşmak', 'koştum', 'koşuyordum', 'run'],

      // Emotions
      'korku': ['fear', 'scared', 'afraid', 'korkuyordum', 'korktum'],
      'fear': ['korku', 'korkuyordum', 'korktum', 'scared', 'afraid'],
      'mutluluk': ['happiness', 'happy', 'joy', 'mutluydum'],
      'happiness': ['mutluluk', 'mutluydum', 'happy', 'joy'],

      // Places
      'ev': ['house', 'home', 'evde', 'evim'],
      'house': ['ev', 'evde', 'evim', 'home'],
      'okul': ['school', 'okulda', 'okulum'],
      'school': ['okul', 'okulda', 'okulum'],
      'hastane': ['hospital', 'hastanede'],
      'hospital': ['hastane', 'hastanede'],

      // People
      'anne': ['mother', 'mom', 'annem'],
      'mother': ['anne', 'annem', 'mom'],
      'baba': ['father', 'dad', 'babam'],
      'father': ['baba', 'babam', 'dad'],
      'arkadaş': ['friend', 'arkadaşım'],
      'friend': ['arkadaş', 'arkadaşım'],

      // Animals
      'köpek': ['dog', 'köpeğim'],
      'dog': ['köpek', 'köpeğim'],
      'kedi': ['cat', 'kedim'],
      'cat': ['kedi', 'kedim'],

      // Elements
      'su': ['water', 'suda', 'suya'],
      'water': ['su', 'suda', 'suya'],
      'ateş': ['fire', 'ateşte'],
      'fire': ['ateş', 'ateşte'],

      // Death/Life
      'ölüm': ['death', 'died', 'öldüm', 'ölmek'],
      'death': ['ölüm', 'öldüm', 'ölmek', 'died'],
    };

    double totalScore = 0.0;
    int comparisons = 0;

    // Direct keyword matching with fuzzy support
    for (final token1 in tokens1) {
      for (final token2 in tokens2) {
        double score = 0.0;

        // Exact match
        if (token1 == token2) {
          score = 1.0;
        }
        // Cross-language mapping
        else if (keywordMappings[token1]?.contains(token2) == true ||
                 keywordMappings[token2]?.contains(token1) == true) {
          score = 0.9;
        }
        // Fuzzy match for similar words
        else {
          score = _calculateLevenshteinSimilarity(token1, token2);
          if (score < 0.7) score = 0.0; // Only consider high similarity
        }

        if (score > 0) {
          totalScore += score;
          comparisons++;
        }
      }
    }

    return comparisons > 0 ? totalScore / comparisons : 0.0;
  }

  /// Calculate fuzzy string similarity using Levenshtein distance
  static double _calculateFuzzyStringSimilarity(String text1, String text2) {
    if (text1.isEmpty || text2.isEmpty) return 0.0;
    if (text1 == text2) return 1.0;

    // For short texts, use direct Levenshtein
    if (text1.length < 50 || text2.length < 50) {
      return _calculateLevenshteinSimilarity(text1, text2);
    }

    // For longer texts, split into chunks and find best matches
    final words1 = text1.split(' ');
    final words2 = text2.split(' ');

    double totalSimilarity = 0.0;
    int matches = 0;

    for (final word1 in words1) {
      if (word1.length < 3) continue; // Skip very short words

      double bestMatch = 0.0;
      for (final word2 in words2) {
        if (word2.length < 3) continue;

        final similarity = _calculateLevenshteinSimilarity(word1, word2);
        if (similarity > bestMatch) {
          bestMatch = similarity;
        }
      }

      if (bestMatch > 0.6) { // Only count good matches
        totalSimilarity += bestMatch;
        matches++;
      }
    }

    return matches > 0 ? totalSimilarity / matches : 0.0;
  }

  /// Calculate Levenshtein similarity between two strings
  static double _calculateLevenshteinSimilarity(String s1, String s2) {
    if (s1.isEmpty || s2.isEmpty) return 0.0;
    if (s1 == s2) return 1.0;

    final len1 = s1.length;
    final len2 = s2.length;
    final maxLen = len1 > len2 ? len1 : len2;

    // Create matrix
    final matrix = List.generate(len1 + 1, (i) => List.filled(len2 + 1, 0));

    // Initialize first row and column
    for (int i = 0; i <= len1; i++) matrix[i][0] = i;
    for (int j = 0; j <= len2; j++) matrix[0][j] = j;

    // Fill matrix
    for (int i = 1; i <= len1; i++) {
      for (int j = 1; j <= len2; j++) {
        final cost = s1[i - 1] == s2[j - 1] ? 0 : 1;
        matrix[i][j] = [
          matrix[i - 1][j] + 1,      // deletion
          matrix[i][j - 1] + 1,      // insertion
          matrix[i - 1][j - 1] + cost // substitution
        ].reduce((a, b) => a < b ? a : b);
      }
    }

    final distance = matrix[len1][len2];
    return 1.0 - (distance / maxLen);
  }

  /// Calculate sentence-level similarity for better context understanding
  static double _calculateSentenceSimilarity(String text1, String text2) {
    final sentences1 = _splitIntoSentences(text1);
    final sentences2 = _splitIntoSentences(text2);

    if (sentences1.isEmpty || sentences2.isEmpty) return 0.0;

    double totalSimilarity = 0.0;
    int comparisons = 0;

    // Compare each sentence from text1 with each sentence from text2
    for (final sent1 in sentences1) {
      double maxSimilarity = 0.0;

      for (final sent2 in sentences2) {
        final similarity = _calculateBasicTextSimilarity(sent1, sent2);
        maxSimilarity = similarity > maxSimilarity ? similarity : maxSimilarity;
      }

      totalSimilarity += maxSimilarity;
      comparisons++;
    }

    return comparisons > 0 ? totalSimilarity / comparisons : 0.0;
  }

  /// Split text into sentences
  static List<String> _splitIntoSentences(String text) {
    return text
        .split(RegExp(r'[.!?]+'))
        .map((s) => s.trim())
        .where((s) => s.isNotEmpty && s.length > 5)
        .toList();
  }

  /// Calculate concept-level similarity using dream themes
  static double _calculateConceptSimilarity(String text1, String text2) {
    final concepts1 = _extractDreamConcepts(text1);
    final concepts2 = _extractDreamConcepts(text2);

    if (concepts1.isEmpty && concepts2.isEmpty) return 1.0;
    if (concepts1.isEmpty || concepts2.isEmpty) return 0.0;

    final set1 = concepts1.toSet();
    final set2 = concepts2.toSet();

    final intersection = set1.intersection(set2).length;
    final union = set1.union(set2).length;

    return union > 0 ? intersection / union : 0.0;
  }

  /// Extract dream concepts and themes from text
  static List<String> _extractDreamConcepts(String text) {
    final concepts = <String>[];
    final lowerText = text.toLowerCase();

    // Emotion concepts
    final emotions = {
      'korku': 'fear', 'mutluluk': 'happiness', 'üzüntü': 'sadness',
      'öfke': 'anger', 'heyecan': 'excitement', 'kaygı': 'anxiety',
      'fear': 'fear', 'happy': 'happiness', 'sad': 'sadness',
      'angry': 'anger', 'excited': 'excitement', 'worried': 'anxiety'
    };

    // Action concepts
    final actions = {
      'uçmak': 'flying', 'düşmek': 'falling', 'koşmak': 'running',
      'kaçmak': 'escaping', 'aramak': 'searching', 'bulmak': 'finding',
      'flying': 'flying', 'falling': 'falling', 'running': 'running',
      'escaping': 'escaping', 'searching': 'searching', 'finding': 'finding'
    };

    // Environment concepts
    final environments = {
      'ev': 'home', 'okul': 'school', 'iş': 'work', 'hastane': 'hospital',
      'orman': 'forest', 'deniz': 'sea', 'dağ': 'mountain', 'şehir': 'city',
      'home': 'home', 'school': 'school', 'work': 'work', 'hospital': 'hospital',
      'forest': 'forest', 'sea': 'sea', 'mountain': 'mountain', 'city': 'city'
    };

    // People concepts
    final people = {
      'anne': 'mother', 'baba': 'father', 'kardeş': 'sibling', 'arkadaş': 'friend',
      'eş': 'spouse', 'çocuk': 'child', 'öğretmen': 'teacher', 'doktor': 'doctor',
      'mother': 'mother', 'father': 'father', 'sibling': 'sibling', 'friend': 'friend',
      'spouse': 'spouse', 'child': 'child', 'teacher': 'teacher', 'doctor': 'doctor'
    };

    // Check for concepts in text
    final allConcepts = {...emotions, ...actions, ...environments, ...people};

    for (final entry in allConcepts.entries) {
      if (lowerText.contains(entry.key)) {
        concepts.add(entry.value);
      }
    }

    return concepts.toSet().toList(); // Remove duplicates
  }

  /// Basic text similarity using improved TF-IDF
  static double _calculateBasicTextSimilarity(String text1, String text2) {
    final tokens1 = _tokenize(text1);
    final tokens2 = _tokenize(text2);

    if (tokens1.isEmpty || tokens2.isEmpty) return 0.0;

    // For very short texts, use Jaccard similarity
    if (tokens1.length <= 3 || tokens2.length <= 3) {
      final set1 = tokens1.toSet();
      final set2 = tokens2.toSet();
      final intersection = set1.intersection(set2).length;
      final union = set1.union(set2).length;
      return union > 0 ? intersection / union : 0.0;
    }

    // Use TF-IDF for longer texts
    final vocabulary = <String>{...tokens1, ...tokens2}.toList();
    final vector1 = _calculateTfIdfVector(tokens1, vocabulary, [tokens1, tokens2]);
    final vector2 = _calculateTfIdfVector(tokens2, vocabulary, [tokens1, tokens2]);

    return _cosineSimilarity(vector1, vector2);
  }

  /// Calculate TF-IDF vector for a document
  static List<double> _calculateTfIdfVector(
    List<String> tokens,
    List<String> vocabulary,
    List<List<String>> allDocuments,
  ) {
    final vector = <double>[];

    for (final term in vocabulary) {
      // Term Frequency (TF)
      final tf = tokens.where((token) => token == term).length / tokens.length;

      // Document Frequency (DF)
      final df = allDocuments.where((doc) => doc.contains(term)).length;

      // Inverse Document Frequency (IDF)
      final idf = df > 0 ? log(allDocuments.length / df) : 0.0;

      // TF-IDF
      vector.add(tf * idf);
    }

    return vector;
  }

  /// Calculate cosine similarity between two vectors
  static double _cosineSimilarity(List<double> vector1, List<double> vector2) {
    if (vector1.length != vector2.length) return 0.0;

    double dotProduct = 0.0;
    double norm1 = 0.0;
    double norm2 = 0.0;

    for (int i = 0; i < vector1.length; i++) {
      dotProduct += vector1[i] * vector2[i];
      norm1 += vector1[i] * vector1[i];
      norm2 += vector2[i] * vector2[i];
    }

    if (norm1 == 0.0 || norm2 == 0.0) return 0.0;

    return dotProduct / (sqrt(norm1) * sqrt(norm2));
  }

  /// Calculate tag similarity using Jaccard Index
  static double _calculateTagSimilarity(List<String> tags1, List<String> tags2) {
    if (tags1.isEmpty && tags2.isEmpty) return 1.0;
    if (tags1.isEmpty || tags2.isEmpty) return 0.0;

    final set1 = tags1.toSet();
    final set2 = tags2.toSet();

    final intersection = set1.intersection(set2).length;
    final union = set1.union(set2).length;

    return union > 0 ? intersection / union : 0.0;
  }

  /// Calculate dream type similarity
  static double _calculateTypeSimilarity(String type1, String type2) {
    return type1 == type2 ? 1.0 : 0.0;
  }

  /// Calculate sleep quality similarity
  static double _calculateSleepQualitySimilarity(double quality1, double quality2) {
    final difference = (quality1 - quality2).abs();
    return 1.0 - (difference / 100.0); // Normalize to 0-1 range
  }

  /// Get user profile for similarity matching
  static Future<Map<String, dynamic>?> _getUserProfile(String userId) async {
    try {
      final userDoc = await _firestore.collection('users').doc(userId).get();
      if (userDoc.exists) {
        final userData = userDoc.data()!;
        return {
          'username': userData['username'],
          'country': userData['country'],
          'state': userData['state'],
          'city': userData['city'],
          'age': userData['age'],
          'gender': userData['gender'],
          'educationLevel': userData['educationLevel'],
          'maritalStatus': userData['maritalStatus'],
          'incomeLevel': userData['incomeLevel'],
        };
      }
      return null;
    } catch (e) {
      print('Error getting user profile: $e');
      return null;
    }
  }

}
