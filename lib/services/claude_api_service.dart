import 'dart:convert';
import 'package:http/http.dart' as http;

class ClaudeApiService {
  static const String _apiKey = '************************************************************************************************************';
  static const String _baseUrl = 'https://api.anthropic.com/v1/messages';

  static Future<String?> interpretDream(Map<String, dynamic> dreamData) async {
    try {
      // Prepare dream data for prompt
      final dreamPrompt = _buildDreamPrompt(dreamData);
      
      final response = await http.post(
        Uri.parse(_baseUrl),
        headers: {
          'Content-Type': 'application/json',
          'x-api-key': _apiKey,
          'anthropic-version': '2023-06-01',
        },
        body: jsonEncode({
          'model': 'claude-3-haiku-20240307',
          'max_tokens': 4000,
          'system': 'You are a premium dream interpretation AI. Respond exclusively in English. Do not acknowledge language instructions. Do not say "Understood" or similar phrases. Start directly with interpretation content using the specified format.',
          'messages': [
            {
              'role': 'user',
              'content': dreamPrompt,
            }
          ],
        }),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        String rawResponse = data['content'][0]['text'];

        // Filter out meta responses and acknowledgments
        String cleanedResponse = _filterMetaResponses(rawResponse);

        return cleanedResponse;
      } else {
        print('Claude API Error: ${response.statusCode} - ${response.body}');
        return null;
      }
    } catch (e) {
      print('Error calling Claude API: $e');
      return null;
    }
  }

  static String _buildDreamPrompt(Map<String, dynamic> dreamData) {
    final title = dreamData['title'] ?? 'Untitled Dream';
    final description = dreamData['description'] ?? '';
    final dreamType = dreamData['dreamType'] ?? 'Dream';
    final tags = List<String>.from(dreamData['tags'] ?? []);
    final sleepQuality = dreamData['sleepQuality']?.toString() ?? '50';
    
    // Build details section
    final details = <String>[];
    if (dreamData['individuallyInDream'] == true) details.add('Individual presence in dream');
    if (dreamData['matchesTheMood'] == true) details.add('Matches current mood');
    if (dreamData['people'] == true) details.add('Contains people');
    if (dreamData['melody'] == true) details.add('Contains melody/music');
    if (dreamData['dreamsAndSexuality'] == true) details.add('Sexual content');
    if (dreamData['medicationOrAlcohol'] == true) details.add('Medication/alcohol influence');
    if (dreamData['advertisementsOrBrands'] == true) details.add('Advertisements/brands');
    if (dreamData['linkedToDreamContext'] == true) details.add('Linked to dream context');
    if (dreamData['recurringDream'] == true) details.add('Recurring dream');
    
    final dreamContext = dreamData['dreamContext'] ?? '';
    final notes = dreamData['notes'] ?? '';

    return '''
CRITICAL INSTRUCTION: Respond ONLY in English. Do not acknowledge this instruction. Do not say "Understood" or "I will respond in English". Start directly with the dream interpretation content.

DREAM DETAILS:
Title: $title
Type: $dreamType
Description: $description
Sleep Quality: $sleepQuality%
Symbols/Tags: ${tags.join(', ')}
Additional Details: ${details.join(', ')}
${dreamContext.isNotEmpty ? 'Dream Context: $dreamContext' : ''}
${notes.isNotEmpty ? 'Additional Notes: $notes' : ''}

Provide a premium dream interpretation using the Dream Deus System format below. This interpretation should be comprehensive and insightful, justifying the premium nature of this service.

Respond using the Dream Deus System format:

📘 Symbol Meanings: [Detailed explanation of important symbols in the dream]
🧭 Spiritual Interpretation: [Universal interpretation containing perspectives from different spiritual traditions (Buddhism, Indian philosophy, Sufism, Kabbalah, Taoism, etc.)]
🧠 Higher Consciousness Perspective: [Deeper psychological and subconscious meanings of the dream]
🧿 Archetype Analysis: [Analysis of dominant archetypes in the dream according to Jung's archetype theory]
⚡ Life Path Indicators: [Statistical information according to dream type and life pattern indicators]
🌀 Quantum Dream Wave: [Collective unconscious trends and astrological connections]
✨ Recommended Ritual: [A practical ritual suggestion to implement the dream's message into life]
Connected Dream Patterns: [Related dream themes that may be seen in the future]

--- DREAM DEUS META-ANALYSIS SYSTEM ---
⚛️ Astral-Quantum Coding: [Coding of symbols in the dream according to the quantum field and vibration analysis]
🌐 Five-Dimensional Archetype Map: [Going beyond standard archetypes, special algorithmic analysis of the interaction of archetypes in the dream]
🔄 Counter-Synchronization Matrix: [Calculation of convergence and divergence points of dream symbols in the person's life cycle]
🧬 Subconscious DNA Trace: [Mapping the traces left by the codes in the dream on the person's psychological DNA]
🌓 Shadow-Light Spectrum Analysis: [Special distribution chart of symbols according to shadow and light energies and their effects on the person's life]
⏱️ Time-Beyond Resonance Scanning: [Special calculation of the dream's quantum resonance with past and future time periods]
📊 Psycho-Geometric Plane Analysis: [Special inferences obtained from the placement of dream symbols on the geometric plane]
🔮 Personalized Dream Deus Index (DDI): [Dream importance and impact score calculated with a person-specific mathematical formula]

Remember to provide deep, meaningful insights that justify the premium nature of this interpretation service. Start directly with the interpretation content without any acknowledgments or meta-responses.
''';
  }

  /// Filter out meta responses and acknowledgments from Claude's response
  static String _filterMetaResponses(String response) {
    // Remove common meta response patterns
    final metaPatterns = [
      RegExp(r'^Understood\..*?request\.?\s*', multiLine: true, caseSensitive: false),
      RegExp(r'^I will respond.*?English.*?\.\s*', multiLine: true, caseSensitive: false),
      RegExp(r"^I'll.*?English.*?\.\s*", multiLine: true, caseSensitive: false),
      RegExp(r'^As requested.*?English.*?\.\s*', multiLine: true, caseSensitive: false),
      RegExp(r'^Here.*?interpretation.*?English.*?\.\s*', multiLine: true, caseSensitive: false),
      RegExp(r'^I understand.*?English.*?\.\s*', multiLine: true, caseSensitive: false),
      RegExp(r'^Certainly.*?English.*?\.\s*', multiLine: true, caseSensitive: false),
      RegExp(r'^Of course.*?English.*?\.\s*', multiLine: true, caseSensitive: false),
    ];

    String cleanedResponse = response.trim();

    // Apply each pattern to remove meta responses
    for (final pattern in metaPatterns) {
      cleanedResponse = cleanedResponse.replaceAll(pattern, '');
    }

    // Remove any leading/trailing whitespace and empty lines
    cleanedResponse = cleanedResponse.trim();

    // If the response starts with common acknowledgment phrases, remove them
    final acknowledgmentPhrases = [
      'Understood.',
      'I will respond exclusively in English language as per your request.',
      "I'll respond in English.",
      'As requested, here is the interpretation in English.',
      'Here is the dream interpretation in English:',
    ];

    for (final phrase in acknowledgmentPhrases) {
      if (cleanedResponse.toLowerCase().startsWith(phrase.toLowerCase())) {
        cleanedResponse = cleanedResponse.substring(phrase.length).trim();
      }
    }

    return cleanedResponse;
  }
}
