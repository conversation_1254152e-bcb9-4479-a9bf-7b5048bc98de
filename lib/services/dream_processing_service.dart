import 'package:cloud_firestore/cloud_firestore.dart';
import 'claude_api_service.dart';
import 'dream_matching_service.dart';
import '../app/models/dream_match.dart';

class DreamProcessingService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  /// Process dream interpretation in background
  static Future<void> processDreamInterpretation(String dreamId) async {
    try {
      // Get dream data first
      final dreamDoc = await _firestore.collection('dreams').doc(dreamId).get();
      if (!dreamDoc.exists) {
        throw Exception('Dream not found');
      }

      final dreamData = dreamDoc.data()!;
      final userId = dreamData['userId'];

      // Create interpretation document
      final interpretationRef = _firestore.collection('interpretations').doc();

      // Set initial status
      await interpretationRef.set({
        'dreamId': dreamId,
        'userId': userId,
        'status': 'processing',
        'createdAt': FieldValue.serverTimestamp(),
        'processingStartedAt': FieldValue.serverTimestamp(),
      });

      // Call Claude API for interpretation
      final interpretation = await ClaudeApiService.interpretDream(dreamData);

      if (interpretation != null) {
        // Update interpretation document with result
        await interpretationRef.update({
          'interpretation': interpretation,
          'status': 'completed',
          'processedAt': FieldValue.serverTimestamp(),
        });
      } else {
        // Mark as failed
        await interpretationRef.update({
          'status': 'failed',
          'error': 'Failed to get interpretation from AI',
          'failedAt': FieldValue.serverTimestamp(),
        });
      }
    } catch (e) {
      // Try to update interpretation document if it exists
      try {
        final interpretationQuery = await _firestore
            .collection('interpretations')
            .where('dreamId', isEqualTo: dreamId)
            .limit(1)
            .get();

        if (interpretationQuery.docs.isNotEmpty) {
          await interpretationQuery.docs.first.reference.update({
            'status': 'failed',
            'error': e.toString(),
            'failedAt': FieldValue.serverTimestamp(),
          });
        }
      } catch (updateError) {
        print('Error updating interpretation status: $updateError');
      }

      print('Error processing dream interpretation: $e');
    }
  }

  /// Get interpretation for a dream
  static Stream<QuerySnapshot> getInterpretationStream(String dreamId) {
    return _firestore
        .collection('interpretations')
        .where('dreamId', isEqualTo: dreamId)
        .limit(1)
        .snapshots();
  }

  /// Get interpretation document for a dream
  static Future<DocumentSnapshot?> getInterpretation(String dreamId) async {
    final query = await _firestore
        .collection('interpretations')
        .where('dreamId', isEqualTo: dreamId)
        .limit(1)
        .get();

    return query.docs.isNotEmpty ? query.docs.first : null;
  }

  /// Check if interpretation is ready
  static bool isInterpretationReady(DocumentSnapshot? interpretationDoc) {
    if (interpretationDoc == null || !interpretationDoc.exists) return false;
    final data = interpretationDoc.data() as Map<String, dynamic>?;
    return data?['status'] == 'completed' && data?['interpretation'] != null;
  }

  /// Check if interpretation is processing
  static bool isInterpretationProcessing(DocumentSnapshot? interpretationDoc) {
    if (interpretationDoc == null || !interpretationDoc.exists) return false;
    final data = interpretationDoc.data() as Map<String, dynamic>?;
    return data?['status'] == 'processing';
  }

  /// Check if interpretation failed
  static bool isInterpretationFailed(DocumentSnapshot? interpretationDoc) {
    if (interpretationDoc == null || !interpretationDoc.exists) return false;
    final data = interpretationDoc.data() as Map<String, dynamic>?;
    return data?['status'] == 'failed';
  }

  /// Get interpretation text
  static String? getInterpretationText(DocumentSnapshot? interpretationDoc) {
    if (interpretationDoc == null || !interpretationDoc.exists) return null;
    final data = interpretationDoc.data() as Map<String, dynamic>?;
    return data?['interpretation'];
  }

  /// Get interpretation error
  static String? getInterpretationError(DocumentSnapshot? interpretationDoc) {
    if (interpretationDoc == null || !interpretationDoc.exists) return null;
    final data = interpretationDoc.data() as Map<String, dynamic>?;
    return data?['error'];
  }

  /// Process dream matching in background with bidirectional updates
  static Future<void> processDreamMatching(String dreamId) async {
    try {
      // Update dream status to processing
      await _firestore.collection('dreams').doc(dreamId).update({
        'matchingStatus': 'processing',
        'matchingStartedAt': FieldValue.serverTimestamp(),
      });

      // Get dream data
      final dreamDoc = await _firestore.collection('dreams').doc(dreamId).get();
      if (!dreamDoc.exists) {
        throw Exception('Dream not found');
      }

      final dreamData = dreamDoc.data()!;

      // Find similar dreams
      final matches = await DreamMatchingService.findSimilarDreams(dreamId, dreamData);

      // Process bidirectional matching
      await _processBidirectionalMatching(dreamId, dreamData, matches);

      // Update dream status to completed
      await _firestore.collection('dreams').doc(dreamId).update({
        'matchingStatus': 'completed',
        'matchingCompletedAt': FieldValue.serverTimestamp(),
        'similarDreamsCount': matches.length,
      });

      print('Bidirectional dream matching processed for $dreamId: ${matches.length} matches found');

    } catch (e) {
      // Mark as failed with error
      await _firestore.collection('dreams').doc(dreamId).update({
        'matchingStatus': 'failed',
        'matchingError': e.toString(),
        'matchingFailedAt': FieldValue.serverTimestamp(),
      });
      print('Error processing dream matching: $e');
    }
  }

  /// Process bidirectional matching - update both current dream and matched dreams
  static Future<void> _processBidirectionalMatching(
    String newDreamId,
    Map<String, dynamic> newDreamData,
    List<DreamMatch> matches,
  ) async {
    final batch = _firestore.batch();

    // 1. Clear existing matches for the new dream
    final existingMatches = await _firestore
        .collection('dreams')
        .doc(newDreamId)
        .collection('matches')
        .get();

    for (final doc in existingMatches.docs) {
      batch.delete(doc.reference);
    }

    // 2. Add matches to the new dream
    for (final match in matches) {
      final matchRef = _firestore
          .collection('dreams')
          .doc(newDreamId)
          .collection('matches')
          .doc();

      batch.set(matchRef, match.toMap());
    }

    // 3. Add reverse matches to existing dreams (bidirectional)
    for (final match in matches) {
      final existingDreamId = match.dreamId;

      // Create reverse match data
      final reverseMatch = DreamMatch(
        dreamId: newDreamId,
        userId: newDreamData['userId'],
        dreamData: newDreamData,
        similarityScore: match.similarityScore,
      );

      // Add reverse match to the existing dream
      final reverseMatchRef = _firestore
          .collection('dreams')
          .doc(existingDreamId)
          .collection('matches')
          .doc();

      batch.set(reverseMatchRef, reverseMatch.toMap());

      print('Added bidirectional match: $existingDreamId ↔ $newDreamId (${(match.similarityScore * 100).round()}%)');
    }

    // Commit all changes in a single batch
    await batch.commit();

    print('Bidirectional matching completed: ${matches.length} dreams updated');
  }

  /// Check if matching is ready
  static bool isMatchingReady(Map<String, dynamic> dreamData) {
    final status = dreamData['matchingStatus'];
    return status == 'completed';
  }

  /// Check if matching is processing
  static bool isMatchingProcessing(Map<String, dynamic> dreamData) {
    final status = dreamData['matchingStatus'];
    return status == 'processing';
  }

  /// Check if matching failed
  static bool isMatchingFailed(Map<String, dynamic> dreamData) {
    final status = dreamData['matchingStatus'];
    return status == 'failed';
  }

  /// Refresh matches for existing dreams (useful for retroactive matching)
  static Future<void> refreshMatchesForDream(String dreamId) async {
    try {
      print('Refreshing matches for dream: $dreamId');

      // Get dream data
      final dreamDoc = await _firestore.collection('dreams').doc(dreamId).get();
      if (!dreamDoc.exists) {
        print('Dream not found: $dreamId');
        return;
      }

      final dreamData = dreamDoc.data()!;

      // Find similar dreams
      final matches = await DreamMatchingService.findSimilarDreams(dreamId, dreamData);

      // Update matches (without bidirectional to avoid infinite loops)
      final batch = _firestore.batch();

      // Clear existing matches
      final existingMatches = await _firestore
          .collection('dreams')
          .doc(dreamId)
          .collection('matches')
          .get();

      for (final doc in existingMatches.docs) {
        batch.delete(doc.reference);
      }

      // Add new matches
      for (final match in matches) {
        final matchRef = _firestore
            .collection('dreams')
            .doc(dreamId)
            .collection('matches')
            .doc();

        batch.set(matchRef, match.toMap());
      }

      await batch.commit();

      // Update dream status
      await _firestore.collection('dreams').doc(dreamId).update({
        'matchingStatus': 'completed',
        'matchingCompletedAt': FieldValue.serverTimestamp(),
        'similarDreamsCount': matches.length,
      });

      print('Matches refreshed for $dreamId: ${matches.length} matches found');

    } catch (e) {
      print('Error refreshing matches for $dreamId: $e');
    }
  }
}
