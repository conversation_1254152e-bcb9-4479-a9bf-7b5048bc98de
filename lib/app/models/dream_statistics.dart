class DreamStatistics {
  final int totalDreams;
  final double averageSleepQuality;
  final int dreamStreak;
  final int totalMatches;
  final Map<String, int> dreamTypeDistribution;
  final Map<String, double> sleepQualityTrend;
  final Map<String, int> weeklyActivity;
  final Map<String, int> countryMatches;
  final List<String> topKeywords;
  final double averageDreamLength;
  final int aiInterpretations;
  final double lucidDreamProgress;
  final Map<String, int> emotionalTone;
  final Map<int, int> hourlyActivity;
  final int totalUsers; // For global stats
  final Map<String, double> userRankings; // User's percentile rankings

  DreamStatistics({
    required this.totalDreams,
    required this.averageSleepQuality,
    required this.dreamStreak,
    required this.totalMatches,
    required this.dreamTypeDistribution,
    required this.sleepQualityTrend,
    required this.weeklyActivity,
    required this.countryMatches,
    required this.topKeywords,
    required this.averageDreamLength,
    required this.aiInterpretations,
    required this.lucidDreamProgress,
    required this.emotionalTone,
    required this.hourlyActivity,
    this.totalUsers = 0,
    this.userRankings = const {},
  });
}

enum StatisticsPeriod {
  week,
  month,
  threeMonths,
}

enum StatisticsType {
  global,
  personal,
}

extension StatisticsTypeExtension on StatisticsType {
  String get displayName {
    switch (this) {
      case StatisticsType.global:
        return 'Global Stats';
      case StatisticsType.personal:
        return 'My Stats';
    }
  }

  String get icon {
    switch (this) {
      case StatisticsType.global:
        return '🌍';
      case StatisticsType.personal:
        return '👤';
    }
  }
}

extension StatisticsPeriodExtension on StatisticsPeriod {
  String get displayName {
    switch (this) {
      case StatisticsPeriod.week:
        return 'Last Week';
      case StatisticsPeriod.month:
        return 'Last Month';
      case StatisticsPeriod.threeMonths:
        return 'Last 3 Months';
    }
  }

  int get days {
    switch (this) {
      case StatisticsPeriod.week:
        return 7;
      case StatisticsPeriod.month:
        return 30;
      case StatisticsPeriod.threeMonths:
        return 90;
    }
  }
}
