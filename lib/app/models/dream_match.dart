import 'package:cloud_firestore/cloud_firestore.dart';

class DreamMatch {
  final String dreamId;
  final String userId;
  final Map<String, dynamic> dreamData;
  final double similarityScore;

  DreamMatch({
    required this.dreamId,
    required this.userId,
    required this.dreamData,
    required this.similarityScore,
  });

  // Getters for easy access to dream data
  String get title => dreamData['title'] ?? 'Untitled Dream';
  String get description => dreamData['description'] ?? '';
  String get dreamType => dreamData['dreamType'] ?? 'Dream';
  DateTime get date => (dreamData['date'] as Timestamp?)?.toDate() ?? DateTime.now();
  List<String> get tags => List<String>.from(dreamData['tags'] ?? []);
  double get sleepQuality => dreamData['sleepQuality']?.toDouble() ?? 50.0;

  // User profile data will be fetched from UI using userId

  // Similarity percentage for display (minimum 70% shown to user)
  String get similarityPercentage {
    final actualPercentage = (similarityScore * 100).round();

    // If similarity is between 50-70%, show 70%
    // If similarity is above 70%, show actual value
    if (actualPercentage >= 50 && actualPercentage < 70) {
      return '70%';
    } else {
      return '$actualPercentage%';
    }
  }

  // Get actual similarity percentage (for debugging/admin purposes)
  String get actualSimilarityPercentage => '${(similarityScore * 100).round()}%';

  // Profile description will be generated in UI using userId

  // Dream type icon path
  String get dreamTypeIcon {
    switch (dreamType) {
      case 'Nightmare':
        return 'assets/images/triangle(1).png';
      case 'Lucid':
        return 'assets/images/triangle(2).png';
      case 'Prophetic':
        return 'assets/images/magic-ball.png';
      default:
        return 'assets/images/pyramid.png';
    }
  }

  // Convert to map for Firestore storage
  Map<String, dynamic> toMap() {
    return {
      'dreamId': dreamId,
      'userId': userId,
      'dreamData': dreamData,
      'similarityScore': similarityScore,
    };
  }

  // Create from Firestore document
  factory DreamMatch.fromMap(Map<String, dynamic> map) {
    return DreamMatch(
      dreamId: map['dreamId'] ?? '',
      userId: map['userId'] ?? '',
      dreamData: Map<String, dynamic>.from(map['dreamData'] ?? {}),
      similarityScore: map['similarityScore']?.toDouble() ?? 0.0,
    );
  }

  // Create from Firestore document snapshot
  factory DreamMatch.fromDocument(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return DreamMatch.fromMap(data);
  }
}
