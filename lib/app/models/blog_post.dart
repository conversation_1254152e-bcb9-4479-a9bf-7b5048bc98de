class BlogPost {
  final String id;
  final String title;
  final String content;
  final String excerpt;
  final String imageUrl;
  final String author;
  final DateTime publishedAt;
  final List<String> tags;
  final int readTime; // in minutes

  BlogPost({
    required this.id,
    required this.title,
    required this.content,
    required this.excerpt,
    required this.imageUrl,
    required this.author,
    required this.publishedAt,
    required this.tags,
    required this.readTime,
  });

  factory BlogPost.fromJson(Map<String, dynamic> json) {
    return BlogPost(
      id: json['id'] ?? '',
      title: json['title'] ?? '',
      content: json['content'] ?? '',
      excerpt: json['excerpt'] ?? '',
      imageUrl: json['imageUrl'] ?? '',
      author: json['author'] ?? '',
      publishedAt: DateTime.parse(json['publishedAt']),
      tags: List<String>.from(json['tags'] ?? []),
      readTime: json['readTime'] ?? 5,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'content': content,
      'excerpt': excerpt,
      'imageUrl': imageUrl,
      'author': author,
      'publishedAt': publishedAt.toIso8601String(),
      'tags': tags,
      'readTime': readTime,
    };
  }
}
