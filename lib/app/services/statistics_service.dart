import 'dart:math';
import '../models/dream_statistics.dart';

class StatisticsService {
  static DreamStatistics getDummyStatistics(StatisticsPeriod period, StatisticsType type) {
    final random = Random();

    // Base values that change based on period
    final multiplier = period == StatisticsPeriod.week ? 1 :
                     period == StatisticsPeriod.month ? 4 : 12;

    // Different data for global vs personal
    final isGlobal = type == StatisticsType.global;
    
    return DreamStatistics(
      totalDreams: isGlobal
          ? (50000 + random.nextInt(10000)) * multiplier
          : (5 + random.nextInt(10)) * multiplier,
      averageSleepQuality: isGlobal
          ? 3.8 + random.nextDouble() * 0.4  // Global: 3.8-4.2
          : 3.2 + random.nextDouble() * 1.8, // Personal: 3.2-5.0
      dreamStreak: isGlobal
          ? random.nextInt(50) + 10
          : random.nextInt(15) + 1,
      totalMatches: isGlobal
          ? (25000 + random.nextInt(5000)) * multiplier
          : (2 + random.nextInt(8)) * multiplier,
      
      dreamTypeDistribution: isGlobal ? {
        'Normal': 45 + random.nextInt(10),     // Global more stable
        'Nightmare': 20 + random.nextInt(5),
        'Lucid': 15 + random.nextInt(5),
        'Prophetic': 8 + random.nextInt(3),
      } : {
        'Normal': 45 + random.nextInt(20),     // Personal more varied
        'Nightmare': 15 + random.nextInt(15),
        'Lucid': 10 + random.nextInt(15),
        'Prophetic': 5 + random.nextInt(10),
      },
      
      sleepQualityTrend: _generateSleepQualityTrend(period),
      weeklyActivity: _generateWeeklyActivity(),
      countryMatches: _generateCountryMatches(),
      
      topKeywords: [
        'flying', 'water', 'family', 'house', 'car', 'school', 
        'work', 'friends', 'animals', 'nature'
      ]..shuffle(),
      
      averageDreamLength: isGlobal
          ? 180 + random.nextDouble() * 40  // Global: 180-220 words
          : 150 + random.nextDouble() * 200, // Personal: 150-350 words
      aiInterpretations: isGlobal
          ? (10000 + random.nextInt(5000)) * multiplier
          : (1 + random.nextInt(5)) * multiplier,
      lucidDreamProgress: isGlobal
          ? 25 + random.nextDouble() * 15  // Global: 25-40%
          : random.nextDouble() * 100, // Personal: 0-100%
      
      emotionalTone: isGlobal ? {
        'Positive': 42 + random.nextInt(8),   // Global more stable
        'Neutral': 35 + random.nextInt(8),
        'Negative': 23 + random.nextInt(8),
      } : {
        'Positive': 40 + random.nextInt(20),  // Personal more varied
        'Neutral': 30 + random.nextInt(20),
        'Negative': 20 + random.nextInt(15),
      },

      hourlyActivity: _generateHourlyActivity(),
      totalUsers: isGlobal ? 50000 + random.nextInt(10000) : 0,
      userRankings: isGlobal ? {} : _generateUserRankings(),
    );
  }
  
  static Map<String, double> _generateSleepQualityTrend(StatisticsPeriod period) {
    final random = Random();
    final trend = <String, double>{};
    final days = period.days;
    
    for (int i = 0; i < days; i++) {
      final date = DateTime.now().subtract(Duration(days: days - i - 1));
      final key = '${date.month}/${date.day}';
      trend[key] = 2.5 + random.nextDouble() * 2.5; // 2.5-5.0
    }
    
    return trend;
  }
  
  static Map<String, int> _generateWeeklyActivity() {
    final random = Random();
    return {
      'Mon': random.nextInt(5),
      'Tue': random.nextInt(5),
      'Wed': random.nextInt(5),
      'Thu': random.nextInt(5),
      'Fri': random.nextInt(5),
      'Sat': random.nextInt(5),
      'Sun': random.nextInt(5),
    };
  }
  
  static Map<String, int> _generateCountryMatches() {
    final random = Random();
    final countries = ['Turkey', 'United States', 'Germany', 'France', 'Japan', 'Brazil'];
    final matches = <String, int>{};
    
    for (final country in countries) {
      if (random.nextBool()) {
        matches[country] = random.nextInt(10) + 1;
      }
    }
    
    return matches;
  }
  
  static Map<int, int> _generateHourlyActivity() {
    final random = Random();
    final activity = <int, int>{};
    
    // Most dreams occur during REM sleep (early morning)
    for (int hour = 0; hour < 24; hour++) {
      if (hour >= 22 || hour <= 8) {
        activity[hour] = random.nextInt(8) + 1; // Higher activity during sleep hours
      } else {
        activity[hour] = random.nextInt(3); // Lower activity during wake hours
      }
    }
    
    return activity;
  }

  static Map<String, double> _generateUserRankings() {
    final random = Random();
    return {
      'dreamFrequency': 60 + random.nextDouble() * 35, // 60-95th percentile
      'sleepQuality': 45 + random.nextDouble() * 40,   // 45-85th percentile
      'lucidDreaming': 25 + random.nextDouble() * 50,  // 25-75th percentile
      'dreamRecall': 70 + random.nextDouble() * 25,    // 70-95th percentile
      'matchSuccess': 30 + random.nextDouble() * 40,   // 30-70th percentile
    };
  }
  
  static List<Achievement> getAchievements(DreamStatistics stats) {
    final achievements = <Achievement>[];
    
    if (stats.totalDreams >= 10) {
      achievements.add(Achievement(
        title: 'Dream Explorer',
        description: 'Recorded ${stats.totalDreams} dreams',
        icon: '🌟',
        isUnlocked: true,
      ));
    }
    
    if (stats.dreamStreak >= 7) {
      achievements.add(Achievement(
        title: 'Dream Streak',
        description: '${stats.dreamStreak} days in a row',
        icon: '🔥',
        isUnlocked: true,
      ));
    }
    
    if (stats.totalMatches >= 5) {
      achievements.add(Achievement(
        title: 'Dream Connector',
        description: 'Found ${stats.totalMatches} dream matches',
        icon: '🤝',
        isUnlocked: true,
      ));
    }
    
    if (stats.lucidDreamProgress >= 50) {
      achievements.add(Achievement(
        title: 'Lucid Master',
        description: 'Mastering lucid dreaming',
        icon: '🧠',
        isUnlocked: true,
      ));
    }
    
    return achievements;
  }
}

class Achievement {
  final String title;
  final String description;
  final String icon;
  final bool isUnlocked;
  
  Achievement({
    required this.title,
    required this.description,
    required this.icon,
    required this.isUnlocked,
  });
}
