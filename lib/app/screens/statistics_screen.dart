import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:percent_indicator/percent_indicator.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:confetti/confetti.dart';
import 'package:flutter/services.dart';
import '../models/dream_statistics.dart';
import '../services/statistics_service.dart';
import '../widgets/statistics/dream_orb_widget.dart';
import '../widgets/statistics/wave_progress_widget.dart';
import '../widgets/statistics/holographic_card_widget.dart';
import '../widgets/statistics/cosmic_background_widget.dart';
import '../widgets/custom_bottom_navigation.dart';

class StatisticsScreen extends StatefulWidget {
  const StatisticsScreen({super.key});

  @override
  State<StatisticsScreen> createState() => _StatisticsScreenState();
}

class _StatisticsScreenState extends State<StatisticsScreen>
    with TickerProviderStateMixin {
  StatisticsPeriod _selectedPeriod = StatisticsPeriod.month;
  StatisticsType _selectedType = StatisticsType.personal;
  DreamStatistics? _statistics;
  bool _isLoading = true;

  late AnimationController _fadeController;
  late AnimationController _slideController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  late ConfettiController _confettiController;
  bool _hasTriggeredConfetti = false;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _loadStatistics();
  }

  void _setupAnimations() {
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _slideController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fadeController, curve: Curves.easeIn),
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(parent: _slideController, curve: Curves.easeOut));

    _confettiController = ConfettiController(duration: const Duration(seconds: 3));
  }

  void _loadStatistics() {
    setState(() {
      _isLoading = true;
    });

    // Simulate loading delay
    Future.delayed(const Duration(milliseconds: 1000), () {
      if (mounted) {
        setState(() {
          _statistics = StatisticsService.getDummyStatistics(_selectedPeriod, _selectedType);
          _isLoading = false;
        });
        _fadeController.forward();
        _slideController.forward();

        // Trigger confetti for achievements only once
        if (_statistics!.totalDreams > 10 && !_hasTriggeredConfetti) {
          _hasTriggeredConfetti = true;
          Future.delayed(const Duration(milliseconds: 800), () {
            _confettiController.play();
            _triggerHapticFeedback();
          });
        }
      }
    });
  }

  void _onPeriodChanged(StatisticsPeriod period) {
    if (period != _selectedPeriod) {
      setState(() {
        _selectedPeriod = period;
      });
      _fadeController.reset();
      _slideController.reset();
      _loadStatistics();
    }
  }

  void _onTypeChanged(StatisticsType type) {
    if (type != _selectedType) {
      setState(() {
        _selectedType = type;
      });
      _fadeController.reset();
      _slideController.reset();
      _loadStatistics();
    }
  }

  void _triggerHapticFeedback() {
    HapticFeedback.lightImpact();
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _slideController.dispose();
    _confettiController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return CosmicBackgroundWidget(
      child: Scaffold(
        backgroundColor: Colors.transparent,
        appBar: AppBar(
          backgroundColor: Colors.transparent,
          elevation: 0,
          leading: Container(
            margin: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.black.withValues(alpha: 0.3),
              borderRadius: BorderRadius.circular(20),
            ),
            child: IconButton(
              icon: const Icon(Icons.arrow_back, color: Colors.white),
              onPressed: () => Navigator.of(context).pop(),
            ),
          ),
          title: const Text(
            'Dream Statistics',
            style: TextStyle(
              color: Colors.white,
              fontSize: 20,
              fontWeight: FontWeight.w600,
            ),
          ),
          centerTitle: true,
        ),
        body: Stack(
          children: [
            _isLoading ? _buildLoadingState() : _buildStatisticsContent(),
            // Confetti overlay
            Align(
              alignment: Alignment.topCenter,
              child: ConfettiWidget(
                confettiController: _confettiController,
                blastDirection: 1.57, // radians - 90 degrees
                particleDrag: 0.05,
                emissionFrequency: 0.05,
                numberOfParticles: 50,
                gravity: 0.05,
                shouldLoop: false,
                colors: const [
                  Color(0xFF6C5CE7),
                  Color(0xFF4ECDC4),
                  Color(0xFFFFD93D),
                  Color(0xFFFF6B6B),
                  Color(0xFF00D4AA),
                ],
              ),
            ),
          ],
        ),

      ),
    );
  }

  Widget _buildLoadingState() {
    return Container(
      margin: const EdgeInsets.all(20),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Epic loading orb
          DreamOrbWidget(
            value: 50,
            title: 'Loading...',
            primaryColor: const Color(0xFF6C5CE7),
            secondaryColor: const Color(0xFF4ECDC4),
            size: 120,
          ).animate(onPlay: (controller) => controller.repeat())
            .rotate(duration: 2000.ms)
            .scale(begin: const Offset(0.8, 0.8), end: const Offset(1.2, 1.2), duration: 1000.ms)
            .then()
            .scale(begin: const Offset(1.2, 1.2), end: const Offset(0.8, 0.8), duration: 1000.ms),

          const SizedBox(height: 30),

          // Animated loading text
          Text(
            _selectedType == StatisticsType.global
                ? 'Connecting to dream universe...'
                : 'Analyzing your dreams...',
            style: const TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.w500,
            ),
          ).animate(onPlay: (controller) => controller.repeat())
            .shimmer(duration: 2000.ms, color: const Color(0xFF6C5CE7))
            .fadeIn(duration: 800.ms),

          const SizedBox(height: 20),

          // Loading dots
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: List.generate(3, (index) {
              return Container(
                margin: const EdgeInsets.symmetric(horizontal: 4),
                child: Container(
                  width: 8,
                  height: 8,
                  decoration: const BoxDecoration(
                    color: Color(0xFF6C5CE7),
                    shape: BoxShape.circle,
                  ),
                ).animate(onPlay: (controller) => controller.repeat())
                  .scale(
                    begin: const Offset(0.5, 0.5),
                    end: const Offset(1.5, 1.5),
                    duration: 600.ms,
                    delay: Duration(milliseconds: index * 200),
                  )
                  .then()
                  .scale(
                    begin: const Offset(1.5, 1.5),
                    end: const Offset(0.5, 0.5),
                    duration: 600.ms,
                  ),
              );
            }),
          ),
        ],
      ),
    );
  }

  Widget _buildStatisticsContent() {
    if (_statistics == null) return const SizedBox();

    return FadeTransition(
      opacity: _fadeAnimation,
      child: SlideTransition(
        position: _slideAnimation,
        child: AnimationLimiter(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
              _buildPeriodSelector(),
              const SizedBox(height: 16),
              _buildTypeSelector(),
              const SizedBox(height: 20),
              if (_selectedType == StatisticsType.global) _buildGlobalHeader(),
              if (_selectedType == StatisticsType.personal) _buildPersonalHeader(),
              const SizedBox(height: 20),
              _buildQuickStatsGrid(),
              const SizedBox(height: 20),
              _buildDreamTypeChart(),
              const SizedBox(height: 20),
              _buildSleepQualityChart(),
              const SizedBox(height: 20),
              _buildWeeklyActivityHeatmap(),
              const SizedBox(height: 20),
              if (_selectedType == StatisticsType.personal) ...[
                _buildUserRankings(),
                const SizedBox(height: 20),
              ],
              _buildProgressIndicators(),
              const SizedBox(height: 20),
              _buildAchievements(),
              const SizedBox(height: 100), // Extra space for bottom navigation
              ].map((widget) => AnimationConfiguration.staggeredList(
                position: 0,
                duration: const Duration(milliseconds: 600),
                child: SlideAnimation(
                  verticalOffset: 50.0,
                  child: FadeInAnimation(child: widget),
                ),
              )).toList(),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildPeriodSelector() {
    return Container(
      padding: const EdgeInsets.all(4),
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.6),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.1),
          width: 1,
        ),
      ),
      child: Row(
        children: StatisticsPeriod.values.map((period) {
          final isSelected = period == _selectedPeriod;
          return Expanded(
            child: GestureDetector(
              onTap: () => _onPeriodChanged(period),
              child: AnimatedContainer(
                duration: const Duration(milliseconds: 200),
                padding: const EdgeInsets.symmetric(vertical: 12),
                decoration: BoxDecoration(
                  color: isSelected 
                      ? const Color(0xFF6C5CE7) 
                      : Colors.transparent,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  period.displayName,
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    color: isSelected ? Colors.white : Colors.white70,
                    fontSize: 14,
                    fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                  ),
                ),
              ),
            ),
          );
        }).toList(),
      ),
    );
  }

  Widget _buildTypeSelector() {
    return Container(
      padding: const EdgeInsets.all(4),
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.6),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.1),
          width: 1,
        ),
      ),
      child: Row(
        children: StatisticsType.values.map((type) {
          final isSelected = type == _selectedType;
          return Expanded(
            child: GestureDetector(
              onTap: () => _onTypeChanged(type),
              child: AnimatedContainer(
                duration: const Duration(milliseconds: 200),
                padding: const EdgeInsets.symmetric(vertical: 12),
                decoration: BoxDecoration(
                  color: isSelected
                      ? const Color(0xFF6C5CE7)
                      : Colors.transparent,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      type.icon,
                      style: const TextStyle(fontSize: 16),
                    ),
                    const SizedBox(width: 6),
                    Text(
                      type.displayName,
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        color: isSelected ? Colors.white : Colors.white70,
                        fontSize: 14,
                        fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          );
        }).toList(),
      ),
    );
  }

  Widget _buildGlobalHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            const Color(0xFF4ECDC4).withValues(alpha: 0.8),
            const Color(0xFF44A08D).withValues(alpha: 0.9),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: const Color(0xFF4ECDC4).withValues(alpha: 0.3),
            blurRadius: 20,
            spreadRadius: 5,
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Text(
              '🌍',
              style: TextStyle(fontSize: 24),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Global Dream Community',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  '${_statistics!.totalUsers.toString().replaceAllMapped(RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'), (Match m) => '${m[1]},')}+ dreamers worldwide',
                  style: TextStyle(
                    color: Colors.white.withValues(alpha: 0.9),
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPersonalHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            const Color(0xFF6C5CE7).withValues(alpha: 0.8),
            const Color(0xFF2D3436).withValues(alpha: 0.9),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: const Color(0xFF6C5CE7).withValues(alpha: 0.3),
            blurRadius: 20,
            spreadRadius: 5,
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Text(
              '👤',
              style: TextStyle(fontSize: 24),
            ),
          ),
          const SizedBox(width: 16),
          const Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Your Dream Journey',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(height: 4),
                Text(
                  'Personal insights and progress tracking',
                  style: TextStyle(
                    color: Colors.white70,
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickStatsGrid() {
    return Wrap(
      spacing: 12,
      runSpacing: 16,
      alignment: WrapAlignment.center,
      children: [
        HolographicStatCard(
          title: _selectedType == StatisticsType.global ? 'Total Dreams' : 'My Dreams',
          value: _selectedType == StatisticsType.global
              ? '${(_statistics!.totalDreams / 1000).toStringAsFixed(0)}K+'
              : '${_statistics!.totalDreams}',
          icon: Icons.nights_stay,
          gradientColors: const [Color(0xFF6C5CE7), Color(0xFF2D3436)],
        ),
        HolographicStatCard(
          title: _selectedType == StatisticsType.global ? 'Total Matches' : 'My Matches',
          value: _selectedType == StatisticsType.global
              ? '${(_statistics!.totalMatches / 1000).toStringAsFixed(0)}K+'
              : '${_statistics!.totalMatches}',
          icon: Icons.people,
          gradientColors: const [Color(0xFF00D4AA), Color(0xFF4ECDC4)],
        ),
        HolographicStatCard(
          title: _selectedType == StatisticsType.global ? 'Avg Sleep Quality' : 'My Sleep Quality',
          value: '${_statistics!.averageSleepQuality.toStringAsFixed(1)}/5',
          icon: Icons.bedtime,
          gradientColors: const [Color(0xFFFF6B6B), Color(0xFFFF8E53)],
        ),
        HolographicStatCard(
          title: _selectedType == StatisticsType.global ? 'Avg Streak' : 'My Streak',
          value: '${_statistics!.dreamStreak} days',
          icon: Icons.local_fire_department,
          gradientColors: const [Color(0xFFFFD93D), Color(0xFFFF9500)],
        ),
      ],
    );
  }

  Widget _buildStatCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.6),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.1),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(6),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(6),
                ),
                child: Icon(
                  icon,
                  color: color,
                  size: 18,
                ),
              ),
              const Spacer(),
            ],
          ),
          const SizedBox(height: 8),
          Flexible(
            child: Text(
              value,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),
          const SizedBox(height: 2),
          Flexible(
            child: Text(
              title,
              style: TextStyle(
                color: Colors.white.withValues(alpha: 0.7),
                fontSize: 11,
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDreamTypeChart() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.6),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.1),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: const Color(0xFF6C5CE7).withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.pie_chart,
                  color: Color(0xFF6C5CE7),
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Text(
                _selectedType == StatisticsType.global
                    ? 'Global Dream Types'
                    : 'My Dream Types',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          SizedBox(
            height: 200,
            child: PieChart(
              PieChartData(
                sections: _buildPieChartSections(),
                centerSpaceRadius: 40,
                sectionsSpace: 2,
              ),
            ),
          ),
          const SizedBox(height: 16),
          _buildLegend(),
        ],
      ),
    );
  }

  List<PieChartSectionData> _buildPieChartSections() {
    final colors = [
      const Color(0xFF6C5CE7),
      const Color(0xFFFF6B6B),
      const Color(0xFF4ECDC4),
      const Color(0xFFFFD93D),
    ];

    final types = _statistics!.dreamTypeDistribution.entries.toList();
    final total = types.fold(0, (sum, entry) => sum + entry.value);

    return types.asMap().entries.map((entry) {
      final index = entry.key;
      final data = entry.value;
      final percentage = (data.value / total * 100);

      return PieChartSectionData(
        color: colors[index % colors.length],
        value: data.value.toDouble(),
        title: '${percentage.toStringAsFixed(1)}%',
        radius: 60,
        titleStyle: const TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
      );
    }).toList();
  }

  Widget _buildLegend() {
    final colors = [
      const Color(0xFF6C5CE7),
      const Color(0xFFFF6B6B),
      const Color(0xFF4ECDC4),
      const Color(0xFFFFD93D),
    ];

    final types = _statistics!.dreamTypeDistribution.entries.toList();

    return Wrap(
      spacing: 16,
      runSpacing: 8,
      children: types.asMap().entries.map((entry) {
        final index = entry.key;
        final data = entry.value;

        return Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 12,
              height: 12,
              decoration: BoxDecoration(
                color: colors[index % colors.length],
                shape: BoxShape.circle,
              ),
            ),
            const SizedBox(width: 6),
            Text(
              '${data.key} (${data.value})',
              style: TextStyle(
                color: Colors.white.withValues(alpha: 0.8),
                fontSize: 12,
              ),
            ),
          ],
        );
      }).toList(),
    );
  }

  Widget _buildSleepQualityChart() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.6),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.1),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: const Color(0xFF4ECDC4).withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.trending_up,
                  color: Color(0xFF4ECDC4),
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              const Text(
                'Sleep Quality Trend',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          SizedBox(
            height: 200,
            child: LineChart(
              LineChartData(
                gridData: FlGridData(
                  show: true,
                  drawVerticalLine: false,
                  horizontalInterval: 1,
                  getDrawingHorizontalLine: (value) {
                    return FlLine(
                      color: Colors.white.withValues(alpha: 0.1),
                      strokeWidth: 1,
                    );
                  },
                ),
                titlesData: FlTitlesData(
                  show: true,
                  rightTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
                  topTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
                  bottomTitles: AxisTitles(
                    sideTitles: SideTitles(
                      showTitles: true,
                      reservedSize: 30,
                      interval: 1,
                      getTitlesWidget: (double value, TitleMeta meta) {
                        final entries = _statistics!.sleepQualityTrend.entries.toList();
                        if (value.toInt() < entries.length) {
                          return Text(
                            entries[value.toInt()].key,
                            style: TextStyle(
                              color: Colors.white.withValues(alpha: 0.6),
                              fontSize: 10,
                            ),
                          );
                        }
                        return const Text('');
                      },
                    ),
                  ),
                  leftTitles: AxisTitles(
                    sideTitles: SideTitles(
                      showTitles: true,
                      interval: 1,
                      getTitlesWidget: (double value, TitleMeta meta) {
                        return Text(
                          value.toInt().toString(),
                          style: TextStyle(
                            color: Colors.white.withValues(alpha: 0.6),
                            fontSize: 10,
                          ),
                        );
                      },
                      reservedSize: 28,
                    ),
                  ),
                ),
                borderData: FlBorderData(show: false),
                minX: 0,
                maxX: _statistics!.sleepQualityTrend.length.toDouble() - 1,
                minY: 0,
                maxY: 5,
                lineBarsData: [
                  LineChartBarData(
                    spots: _statistics!.sleepQualityTrend.entries
                        .toList()
                        .asMap()
                        .entries
                        .map((entry) => FlSpot(entry.key.toDouble(), entry.value.value))
                        .toList(),
                    isCurved: true,
                    gradient: const LinearGradient(
                      colors: [Color(0xFF4ECDC4), Color(0xFF44A08D)],
                    ),
                    barWidth: 3,
                    isStrokeCapRound: true,
                    dotData: const FlDotData(show: false),
                    belowBarData: BarAreaData(
                      show: true,
                      gradient: LinearGradient(
                        colors: [
                          const Color(0xFF4ECDC4).withValues(alpha: 0.3),
                          const Color(0xFF44A08D).withValues(alpha: 0.1),
                        ],
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildWeeklyActivityHeatmap() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.6),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.1),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: const Color(0xFFFFD93D).withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.calendar_view_week,
                  color: Color(0xFFFFD93D),
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              const Text(
                'Weekly Dream Activity',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: _statistics!.weeklyActivity.entries.map((entry) {
              final intensity = entry.value / 5.0; // Normalize to 0-1
              return Column(
                children: [
                  Container(
                    width: 30,
                    height: 60,
                    decoration: BoxDecoration(
                      color: Color.lerp(
                        Colors.white.withValues(alpha: 0.1),
                        const Color(0xFFFFD93D),
                        intensity,
                      ),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Center(
                      child: Text(
                        entry.value.toString(),
                        style: TextStyle(
                          color: intensity > 0.5 ? Colors.black : Colors.white,
                          fontWeight: FontWeight.bold,
                          fontSize: 12,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    entry.key,
                    style: TextStyle(
                      color: Colors.white.withValues(alpha: 0.7),
                      fontSize: 10,
                    ),
                  ),
                ],
              );
            }).toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildUserRankings() {
    if (_statistics!.userRankings.isEmpty) return const SizedBox();

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.6),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.1),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: const Color(0xFF6C5CE7).withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.leaderboard,
                  color: Color(0xFF6C5CE7),
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              const Text(
                'You vs World Rankings',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          ...(_statistics!.userRankings.entries.map((entry) {
            return _buildRankingItem(entry.key, entry.value);
          }).toList()),
        ],
      ),
    );
  }

  Widget _buildRankingItem(String category, double percentile) {
    final categoryNames = {
      'dreamFrequency': 'Dream Frequency',
      'sleepQuality': 'Sleep Quality',
      'lucidDreaming': 'Lucid Dreaming',
      'dreamRecall': 'Dream Recall',
      'matchSuccess': 'Match Success',
    };

    final colors = {
      'dreamFrequency': const Color(0xFF6C5CE7),
      'sleepQuality': const Color(0xFFFF6B6B),
      'lucidDreaming': const Color(0xFF4ECDC4),
      'dreamRecall': const Color(0xFFFFD93D),
      'matchSuccess': const Color(0xFF00D4AA),
    };

    final color = colors[category] ?? const Color(0xFF6C5CE7);
    final name = categoryNames[category] ?? category;

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                name,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
              Row(
                children: [
                  Text(
                    '${percentile.toStringAsFixed(0)}th',
                    style: TextStyle(
                      color: color,
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(width: 4),
                  Icon(
                    percentile >= 70 ? Icons.trending_up :
                    percentile >= 50 ? Icons.trending_flat : Icons.trending_down,
                    color: percentile >= 70 ? Colors.green :
                           percentile >= 50 ? Colors.orange : Colors.red,
                    size: 16,
                  ),
                ],
              ),
            ],
          ),
          const SizedBox(height: 8),
          SizedBox(
            width: MediaQuery.of(context).size.width - 80,
            child: LinearPercentIndicator(
              lineHeight: 6.0,
              percent: percentile / 100,
              backgroundColor: Colors.white.withValues(alpha: 0.1),
              progressColor: color,
              barRadius: const Radius.circular(3),
              animation: true,
              animationDuration: 1500,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            percentile >= 80 ? 'Excellent!' :
            percentile >= 60 ? 'Above Average' :
            percentile >= 40 ? 'Average' : 'Room for Improvement',
            style: TextStyle(
              color: Colors.white.withValues(alpha: 0.6),
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProgressIndicators() {
    if (_selectedType == StatisticsType.global) {
      return Column(
        children: [
          Row(
            children: [
              Expanded(
                child: DreamOrbWidget(
                  value: _statistics!.lucidDreamProgress,
                  title: 'Global Lucid Rate',
                  primaryColor: const Color(0xFF6C5CE7),
                  secondaryColor: const Color(0xFF2D3436),
                  size: 140,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: DreamOrbWidget(
                  value: 68.0,
                  title: 'Global Recall Rate',
                  primaryColor: const Color(0xFF4ECDC4),
                  secondaryColor: const Color(0xFF44A08D),
                  size: 140,
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          WaveProgressWidget(
            progress: _statistics!.averageSleepQuality * 20, // Convert to percentage
            title: 'Global Sleep Quality',
            value: '${_statistics!.averageSleepQuality.toStringAsFixed(1)}/5',
            waveColor: const Color(0xFF00D4AA),
            backgroundColor: Colors.black.withValues(alpha: 0.6),
            height: 70,
          ),
        ],
      );
    } else {
      return Column(
        children: [
          Row(
            children: [
              Expanded(
                child: DreamOrbWidget(
                  value: _statistics!.lucidDreamProgress,
                  title: 'My Lucid Progress',
                  primaryColor: const Color(0xFF6C5CE7),
                  secondaryColor: const Color(0xFF2D3436),
                  size: 140,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: DreamOrbWidget(
                  value: 75.0,
                  title: 'My Recall Ability',
                  primaryColor: const Color(0xFF4ECDC4),
                  secondaryColor: const Color(0xFF44A08D),
                  size: 140,
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          WaveProgressWidget(
            progress: _statistics!.averageSleepQuality * 20, // Convert to percentage
            title: 'My Sleep Quality',
            value: '${_statistics!.averageSleepQuality.toStringAsFixed(1)}/5',
            waveColor: const Color(0xFFFF6B6B),
            backgroundColor: Colors.black.withValues(alpha: 0.6),
            height: 70,
          ),
        ],
      );
    }
  }

  Widget _buildProgressCard({
    required String title,
    required double value,
    required Color color,
    required IconData icon,
    String? subtitle,
  }) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.6),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.1),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(
              icon,
              color: color,
              size: 24,
            ),
          ),
          const SizedBox(width: 16),
          Flexible(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                if (subtitle != null) ...[
                  const SizedBox(height: 4),
                  Text(
                    subtitle,
                    style: TextStyle(
                      color: Colors.white.withValues(alpha: 0.6),
                      fontSize: 12,
                    ),
                  ),
                ],
                const SizedBox(height: 8),
                SizedBox(
                  width: MediaQuery.of(context).size.width - 140,
                  child: LinearPercentIndicator(
                    lineHeight: 8.0,
                    percent: value / 100,
                    backgroundColor: Colors.white.withValues(alpha: 0.1),
                    progressColor: color,
                    barRadius: const Radius.circular(4),
                    animation: true,
                    animationDuration: 1000,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(width: 16),
          Text(
            '${value.toStringAsFixed(0)}%',
            style: TextStyle(
              color: color,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAchievements() {
    final achievements = StatisticsService.getAchievements(_statistics!);

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.6),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.1),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: const Color(0xFFFFD93D).withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.emoji_events,
                  color: Color(0xFFFFD93D),
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Text(
                _selectedType == StatisticsType.global
                    ? 'Community Milestones'
                    : 'My Achievements',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          if (achievements.isEmpty)
            Center(
              child: Text(
                _selectedType == StatisticsType.global
                    ? 'Community milestones coming soon!'
                    : 'Keep dreaming to unlock achievements!',
                style: TextStyle(
                  color: Colors.white.withValues(alpha: 0.6),
                  fontSize: 14,
                ),
              ),
            )
          else
            Wrap(
              spacing: 12,
              runSpacing: 12,
              children: achievements.map((achievement) {
                return Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  decoration: BoxDecoration(
                    color: const Color(0xFFFFD93D).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: const Color(0xFFFFD93D).withValues(alpha: 0.3),
                      width: 1,
                    ),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        achievement.icon,
                        style: const TextStyle(fontSize: 16),
                      ),
                      const SizedBox(width: 6),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            achievement.title,
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          Text(
                            achievement.description,
                            style: TextStyle(
                              color: Colors.white.withValues(alpha: 0.7),
                              fontSize: 10,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                );
              }).toList(),
            ),
        ],
      ),
    );
  }


}
