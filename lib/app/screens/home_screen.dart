import 'package:flutter/material.dart';
import 'package:thedreamdeus/services/auth_service.dart';
import 'package:thedreamdeus/app/screens/add_dream_screen.dart';
import 'package:thedreamdeus/app/screens/profile_screen.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../models/user_profile.dart';
import '../widgets/custom_bottom_navigation.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  HomeScreenState createState() => HomeScreenState();
}

class HomeScreenState extends State<HomeScreen> {
  final AuthService _authService = AuthService();
  String? _username;
  bool _isAddDreamPressed = false;
  bool _isForgotPressed = false;
  bool _isRemindLaterPressed = false;

  @override
  void initState() {
    super.initState();
    _loadUsername();
  }

  Future<void> _loadUsername() async {
    String? username = await _authService.getCurrentUsername();
    setState(() {
      _username = username ?? 'User';
    });
  }

  String _getGreeting() {
    final hour = DateTime.now().hour;
    if (hour < 12) {
      return 'Good morning';
    } else if (hour < 17) {
      return 'Good afternoon';
    } else {
      return 'Good evening';
    }
  }



  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF1A1A2E),
      body: Container(
        decoration: const BoxDecoration(
          image: DecorationImage(
            image: AssetImage('assets/images/bg.jpg'),
            fit: BoxFit.cover,
          ),
        ),
        child: Stack(
          children: [
            // Dark overlay for better text readability
            Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Colors.black.withValues(alpha: 0.3),
                    Colors.black.withValues(alpha: 0.5),
                  ],
                ),
              ),
            ),
            // Main content
            SafeArea(
              child: Padding(
                padding: const EdgeInsets.all(20.0),
                child: Column(
                  children: [
                    // Top section with profile icon only
                    Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        // Profile icon
                        GestureDetector(
                          onTap: () {
                            Navigator.push(
                              context,
                              MaterialPageRoute(builder: (context) => const ProfileScreen()),
                            );
                          },
                          child: Container(
                            padding: const EdgeInsets.all(8),
                            decoration: BoxDecoration(
                              color: Colors.white.withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(20),
                            ),
                            child: Image.asset(
                              'assets/images/triangle.png',
                              height: 24,
                              width: 24,
                              color: Colors.white,
                            ),
                          ),
                        ),
                      ],
                    ),

                    // Centered content section
                    Expanded(
                      child: Center(
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            // Logo and greeting section
                            Column(
                              children: [
                                // Dream Deus Logo - Made bigger
                                Image.asset(
                                  'assets/images/logo.png',
                                  height: 120,
                                  width: 120,
                                ),
                                const SizedBox(height: 4), // Further reduced to bring text closer
                                Text(
                                  _getGreeting(),
                                  style: const TextStyle(
                                    color: Colors.white70,
                                    fontSize: 18,
                                  ),
                                ),
                                const SizedBox(height: 2), // Reduced from 4 to bring text closer
                                GestureDetector(
                                  onTap: () {
                                    Navigator.push(
                                      context,
                                      MaterialPageRoute(
                                        builder: (context) => const ProfileScreen(),
                                      ),
                                    );
                                  },
                                  child: Text(
                                    _username ?? 'Loading...',
                                    style: const TextStyle(
                                      color: Colors.white,
                                      fontSize: 28,
                                      fontWeight: FontWeight.bold,
                                      decoration: TextDecoration.underline,
                                      decorationColor: Colors.white,
                                    ),
                                  ),
                                ),
                              ],
                            ),

                            const SizedBox(height: 50), // Reduced spacing

                            // Torah icon - Made smaller
                            Image.asset(
                              'assets/images/torah.png',
                              height: 45, // Reduced from 60
                              width: 45,  // Reduced from 60
                            ),

                            const SizedBox(height: 25), // Reduced spacing

                            // Main question
                            const Text(
                              'Do you remember what you\ndreamed of last night?',
                              textAlign: TextAlign.center,
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 24,
                                fontWeight: FontWeight.w500,
                                height: 1.3,
                              ),
                            ),

                            const SizedBox(height: 35), // Reduced spacing

                            // Action buttons
                            Column(
                              children: [
                                // Add dream button - Semi-transparent, full opacity on press
                                AnimatedOpacity(
                                  opacity: _isAddDreamPressed ? 1.0 : 0.7, // Full opacity when pressed, semi-transparent otherwise
                                  duration: const Duration(milliseconds: 150),
                                  child: SizedBox(
                                    width: double.infinity,
                                    child: GestureDetector(
                                      onTapDown: (_) => setState(() => _isAddDreamPressed = true),
                                      onTapUp: (_) => setState(() => _isAddDreamPressed = false),
                                      onTapCancel: () => setState(() => _isAddDreamPressed = false),
                                      child: ElevatedButton(
                                        onPressed: () => _checkProfileAndAddDream(),
                                        style: ElevatedButton.styleFrom(
                                          backgroundColor: const Color(0xFF6C5CE7),
                                          foregroundColor: Colors.white,
                                          padding: const EdgeInsets.symmetric(vertical: 18),
                                          shape: RoundedRectangleBorder(
                                            borderRadius: BorderRadius.circular(15),
                                          ),
                                          elevation: 0,
                                        ),
                                        child: const Text(
                                          'Add dream',
                                          style: TextStyle(
                                            fontSize: 18,
                                            fontWeight: FontWeight.w600,
                                          ),
                                        ),
                                      ),
                                    ),
                                  ),
                                ),

                                const SizedBox(height: 14), // Reduced spacing

                                // I forgot button - Semi-transparent, full opacity on press
                                AnimatedOpacity(
                                  opacity: _isForgotPressed ? 1.0 : 0.7, // Full opacity when pressed, semi-transparent otherwise
                                  duration: const Duration(milliseconds: 150),
                                  child: SizedBox(
                                    width: double.infinity,
                                    child: GestureDetector(
                                      onTapDown: (_) => setState(() => _isForgotPressed = true),
                                      onTapUp: (_) => setState(() => _isForgotPressed = false),
                                      onTapCancel: () => setState(() => _isForgotPressed = false),
                                      child: ElevatedButton(
                                        onPressed: () {
                                          // Handle forgot action
                                        },
                                        style: ElevatedButton.styleFrom(
                                          backgroundColor: Colors.white.withValues(alpha: 0.1),
                                          foregroundColor: Colors.white,
                                          padding: const EdgeInsets.symmetric(vertical: 18),
                                          shape: RoundedRectangleBorder(
                                            borderRadius: BorderRadius.circular(15),
                                          ),
                                          elevation: 0,
                                        ),
                                        child: const Text(
                                          'I forgot',
                                          style: TextStyle(
                                            fontSize: 18,
                                            fontWeight: FontWeight.w500,
                                          ),
                                        ),
                                      ),
                                    ),
                                  ),
                                ),

                                const SizedBox(height: 14), // Reduced spacing

                                // Remind me later button - Semi-transparent, full opacity on press
                                AnimatedOpacity(
                                  opacity: _isRemindLaterPressed ? 1.0 : 0.7, // Full opacity when pressed, semi-transparent otherwise
                                  duration: const Duration(milliseconds: 150),
                                  child: SizedBox(
                                    width: double.infinity,
                                    child: GestureDetector(
                                      onTapDown: (_) => setState(() => _isRemindLaterPressed = true),
                                      onTapUp: (_) => setState(() => _isRemindLaterPressed = false),
                                      onTapCancel: () => setState(() => _isRemindLaterPressed = false),
                                      child: ElevatedButton(
                                        onPressed: () {
                                          // Handle remind later action
                                        },
                                        style: ElevatedButton.styleFrom(
                                          backgroundColor: Colors.white.withValues(alpha: 0.1),
                                          foregroundColor: Colors.white,
                                          padding: const EdgeInsets.symmetric(vertical: 18),
                                          shape: RoundedRectangleBorder(
                                            borderRadius: BorderRadius.circular(15),
                                          ),
                                          elevation: 0,
                                        ),
                                        child: const Text(
                                          'Remind me later',
                                          style: TextStyle(
                                            fontSize: 18,
                                            fontWeight: FontWeight.w500,
                                          ),
                                        ),
                                      ),
                                    ),
                                  ),
                                ),

                                const SizedBox(height: 25), // Added spacing before skip button

                                // Skip button
                                TextButton(
                                  onPressed: () {
                                    // Handle skip action
                                  },
                                  child: const Text(
                                    'Skip',
                                    style: TextStyle(
                                      color: Colors.white54,
                                      fontSize: 16,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _checkProfileAndAddDream() async {
    final user = FirebaseAuth.instance.currentUser;
    if (user != null) {
      try {
        final doc = await FirebaseFirestore.instance
            .collection('users')
            .doc(user.uid)
            .get();

        if (doc.exists) {
          final data = doc.data()!;
          final profile = UserProfile.fromMap(data);

          if (profile.isComplete) {
            // Profile complete, go to add dream
            Navigator.push(
              context,
              MaterialPageRoute(builder: (context) => const AddDreamScreen()),
            );
          } else {
            // Profile incomplete, show dialog
            _showProfileIncompleteDialog();
          }
        } else {
          // User document doesn't exist, show dialog
          _showProfileIncompleteDialog();
        }
      } catch (e) {
        print('Error checking profile: $e');
        // On error, show dialog to be safe
        _showProfileIncompleteDialog();
      }
    }
  }

  void _showProfileIncompleteDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: const Color(0xFF2D1B69),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          title: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: const Color(0xFF6C5CE7),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.person_outline,
                  color: Colors.white,
                  size: 24,
                ),
              ),
              const SizedBox(width: 12),
              const Expanded(
                child: Text(
                  'Complete Your Profile',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
          content: const Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'To help us match you with people who have similar dreams without revealing your personal information, we need some basic demographic details.',
                style: TextStyle(
                  color: Colors.white70,
                  fontSize: 14,
                  height: 1.4,
                ),
              ),
              SizedBox(height: 15),
              Text(
                'Required information:',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                ),
              ),
              SizedBox(height: 8),
              Text(
                '• Age range\n• Gender\n• Location (Country, State, City)\n• Marital status\n• Education level\n• Income level',
                style: TextStyle(
                  color: Colors.white60,
                  fontSize: 13,
                  height: 1.3,
                ),
              ),
              SizedBox(height: 15),
              Text(
                'This information is kept completely anonymous and secure.',
                style: TextStyle(
                  color: Color(0xFF6C5CE7),
                  fontSize: 12,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text(
                'Maybe Later',
                style: TextStyle(color: Colors.white54),
              ),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop(); // Close dialog
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const ProfileScreen(),
                  ),
                );
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF6C5CE7),
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: const Text(
                'Complete Profile',
                style: TextStyle(fontWeight: FontWeight.w600),
              ),
            ),
          ],
        );
      },
    );
  }
}

// Custom painter for background particles effect
class ParticlesPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.white.withValues(alpha: 0.05)
      ..style = PaintingStyle.fill;

    // Draw some scattered particles
    for (int i = 0; i < 20; i++) {
      final x = (i * 37) % size.width;
      final y = (i * 73) % size.height;
      canvas.drawCircle(Offset(x, y), 2, paint);
    }

    // Draw connecting lines
    final linePaint = Paint()
      ..color = Colors.white.withValues(alpha: 0.03)
      ..strokeWidth = 1;

    for (int i = 0; i < 10; i++) {
      final x1 = (i * 47) % size.width;
      final y1 = (i * 83) % size.height;
      final x2 = ((i + 1) * 47) % size.width;
      final y2 = ((i + 1) * 83) % size.height;
      canvas.drawLine(Offset(x1, y1), Offset(x2, y2), linePaint);
    }
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}
