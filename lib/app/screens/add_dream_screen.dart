import 'package:flutter/material.dart';
import 'package:speech_to_text/speech_to_text.dart' as stt;
import 'package:permission_handler/permission_handler.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../models/user_profile.dart';
import 'profile_screen.dart';
import 'dream_records_screen.dart';
import '../../services/dream_processing_service.dart';
import '../widgets/custom_bottom_navigation.dart';

class AddDreamScreen extends StatefulWidget {
  const AddDreamScreen({super.key});

  @override
  AddDreamScreenState createState() => AddDreamScreenState();
}

class AddDreamScreenState extends State<AddDreamScreen> with TickerProviderStateMixin {
  late TabController _tabController;
  final TextEditingController _titleController = TextEditingController();
  final TextEditingController _dreamController = TextEditingController();
  final TextEditingController _tagController = TextEditingController();
  bool _isVoicePressed = false;
  int _currentTabIndex = 0;

  // Details tab state
  String? _selectedDreamType;
  double _dreamRate = 50.0;
  double _dreamLength = 50.0;
  double _sleepQuality = 50.0;

  // Yes/No questions state
  bool? _individuallyInDream;
  bool? _matchesTheMood;
  bool? _people;
  bool? _melody;
  bool? _dreamsAndSexuality;
  bool? _medicationOrAlcohol;
  bool? _advertisementsOrBrands;
  bool? _linkedToDreamContext;
  bool? _recurringDream;

  // Text controllers for additional fields
  final TextEditingController _contextController = TextEditingController();
  final TextEditingController _notesController = TextEditingController();

  // Loading state
  bool _isLoading = false;

  // Additional state variables
  DateTime? _selectedDate;

  // Speech to text
  late stt.SpeechToText _speech;
  bool _isListening = false;
  String _text = '';
  double _confidence = 1.0;

  // Symbols/Tags
  List<String> _selectedTags = [];
  List<String> _suggestedTags = [
    'forest', 'car', 'bee', 'color', 'owl', 'water', 'fire', 'house',
    'animal', 'flying', 'falling', 'running', 'family', 'friend', 'fear',
    'love', 'death', 'birth', 'school', 'work', 'money', 'food'
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _speech = stt.SpeechToText();
  }



  @override
  void dispose() {
    _tabController.dispose();
    _titleController.dispose();
    _dreamController.dispose();
    _tagController.dispose();
    _contextController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  void _listen() async {
    if (!_isListening) {
      // Check microphone permission first
      PermissionStatus permission = await Permission.microphone.status;

      if (permission.isDenied) {
        permission = await Permission.microphone.request();
      }

      if (permission.isPermanentlyDenied) {
        _showPermissionDialog();
        return;
      }

      if (permission.isGranted) {
        try {
          bool available = await _speech.initialize(
            onStatus: (val) => print('onStatus: $val'),
            onError: (val) {
              print('onError: $val');
              setState(() => _isListening = false);
              _showErrorDialog('Speech recognition error: $val');
            },
          );
          if (available) {
            setState(() => _isListening = true);
            _speech.listen(
              onResult: (val) => setState(() {
                _text = val.recognizedWords;
                if (val.hasConfidenceRating && val.confidence > 0) {
                  _confidence = val.confidence;
                }
                // Append to dream text
                if (_text.isNotEmpty) {
                  String currentText = _dreamController.text;
                  if (currentText.isNotEmpty && !currentText.endsWith(' ')) {
                    currentText += ' ';
                  }
                  _dreamController.text = currentText + _text;
                  _dreamController.selection = TextSelection.fromPosition(
                    TextPosition(offset: _dreamController.text.length),
                  );
                }
              }),
            );
          } else {
            _showErrorDialog('Speech recognition not available on this device.');
          }
        } catch (e) {
          print('Speech initialization error: $e');
          setState(() => _isListening = false);
          _showVoiceErrorDialog('Voice dictation is not available on emulator. Please try on a real device.');
        }
      } else {
        _showVoiceErrorDialog('Microphone permission is required for voice dictation.');
      }
    } else {
      setState(() => _isListening = false);
      _speech.stop();
    }
  }

  void _showVoiceErrorDialog(String message) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: const Color(0xFF2A2A3E),
          title: const Text(
            'Voice Dictation',
            style: TextStyle(color: Colors.white),
          ),
          content: Text(
            message,
            style: const TextStyle(color: Colors.white70),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text(
                'OK',
                style: TextStyle(color: Color(0xFF6C5CE7)),
              ),
            ),
          ],
        );
      },
    );
  }

  void _showPermissionDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: const Color(0xFF2A2A3E),
          title: const Text(
            'Microphone Permission',
            style: TextStyle(color: Colors.white),
          ),
          content: const Text(
            'Voice dictation requires microphone access. Please enable microphone permission in your device settings.',
            style: TextStyle(color: Colors.white70),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text(
                'Cancel',
                style: TextStyle(color: Colors.white54),
              ),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                openAppSettings();
              },
              child: const Text(
                'Open Settings',
                style: TextStyle(color: Color(0xFF6C5CE7)),
              ),
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF1A1A2E),
      body: Container(
        decoration: const BoxDecoration(
          image: DecorationImage(
            image: AssetImage('assets/images/bg.jpg'),
            fit: BoxFit.cover,
          ),
        ),
        child: Stack(
          children: [
            // Dark overlay for better text readability
            Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Colors.black.withValues(alpha: 0.4),
                    Colors.black.withValues(alpha: 0.6),
                  ],
                ),
              ),
            ),
            // Main content
            SafeArea(
              child: Column(
                children: [
                  // Top bar with back button and save button
                  Padding(
                    padding: const EdgeInsets.all(20.0),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        // Back button - Apple style
                        GestureDetector(
                          onTap: () => Navigator.pop(context),
                          child: Container(
                            width: 40,
                            height: 40,
                            decoration: BoxDecoration(
                              color: Colors.white.withValues(alpha: 0.1),
                              shape: BoxShape.circle,
                            ),
                            child: const Icon(
                              Icons.chevron_left,
                              color: Colors.white,
                              size: 28,
                            ),
                          ),
                        ),
                        // Save dream button
                        ElevatedButton(
                          onPressed: _isLoading ? null : _saveDream,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.white,
                            foregroundColor: Colors.black,
                            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(25),
                            ),
                            elevation: 0,
                          ),
                          child: _isLoading
                            ? const SizedBox(
                                width: 20,
                                height: 20,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  valueColor: AlwaysStoppedAnimation<Color>(Colors.black),
                                ),
                              )
                            : const Text(
                                'Save dream',
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                        ),
                      ],
                    ),
                  ),

                  // Tab bar - Updated style
                  Container(
                    margin: const EdgeInsets.symmetric(horizontal: 20),
                    child: Row(
                      children: [
                        Expanded(child: _buildTabButton(0, 'Story')),
                        const SizedBox(width: 8),
                        Expanded(child: _buildTabButton(1, 'Details')),
                        const SizedBox(width: 8),
                        Expanded(child: _buildTabButton(2, 'Symbols')),
                      ],
                    ),
                  ),

                  const SizedBox(height: 30),

                  // Tab content - Scrollable
                  Expanded(
                    child: TabBarView(
                      controller: _tabController,
                      children: [
                        // Story tab - Scrollable
                        SingleChildScrollView(
                          child: _buildStoryTab(),
                        ),
                        // Details tab
                        SingleChildScrollView(
                          child: _buildDetailsTab(),
                        ),
                        // Symbols tab
                        SingleChildScrollView(
                          child: _buildSymbolsTab(),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
      bottomNavigationBar: CustomBottomNavigation(
        currentIndex: 1, // Add dream is part of dream records flow
        context: context,
      ),
    );
  }

  Widget _buildTabButton(int index, String title) {
    final isSelected = _currentTabIndex == index;
    return GestureDetector(
      onTap: () {
        setState(() {
          _currentTabIndex = index;
        });
        _tabController.animateTo(index);
      },
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 12),
        decoration: BoxDecoration(
          color: isSelected
            ? const Color(0xFF6C5CE7)
            : const Color(0xFF2A2A3E), // Dark background like in image
          borderRadius: BorderRadius.circular(12), // Less rounded corners
        ),
        child: Text(
          title,
          textAlign: TextAlign.center,
          style: TextStyle(
            color: isSelected ? Colors.white : Colors.white54,
            fontSize: 16,
            fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
          ),
        ),
      ),
    );
  }

  Widget _buildSymbolsTab() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Hash symbol
          const Text(
            '#',
            style: TextStyle(
              color: Colors.white,
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ),

          const SizedBox(height: 20),

          // Add tag input
          Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(25),
            ),
            child: TextField(
              controller: _tagController,
              style: const TextStyle(
                color: Colors.black,
                fontSize: 16,
              ),
              decoration: const InputDecoration(
                hintText: '+ Add a tag and press enter',
                hintStyle: TextStyle(
                  color: Colors.grey,
                  fontSize: 16,
                ),
                border: InputBorder.none,
                contentPadding: EdgeInsets.symmetric(horizontal: 20, vertical: 15),
              ),
              onSubmitted: (value) {
                if (value.trim().isNotEmpty && !_selectedTags.contains(value.trim())) {
                  setState(() {
                    _selectedTags.add(value.trim());
                    _tagController.clear();
                  });
                }
              },
            ),
          ),

          const SizedBox(height: 20),

          // Selected tags
          if (_selectedTags.isNotEmpty) ...[
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: _selectedTags.map((tag) => _buildSelectedTag(tag)).toList(),
            ),
            const SizedBox(height: 20),
          ],

          // Suggested tags
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: _suggestedTags.map((tag) => _buildSuggestedTag(tag)).toList(),
          ),

          const SizedBox(height: 40),
        ],
      ),
    );
  }

  Widget _buildSelectedTag(String tag) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: const Color(0xFF6C5CE7),
        borderRadius: BorderRadius.circular(20),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            tag,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(width: 8),
          GestureDetector(
            onTap: () {
              setState(() {
                _selectedTags.remove(tag);
              });
            },
            child: const Icon(
              Icons.close,
              color: Colors.white,
              size: 16,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSuggestedTag(String tag) {
    final isSelected = _selectedTags.contains(tag);
    return GestureDetector(
      onTap: () {
        if (!isSelected) {
          setState(() {
            _selectedTags.add(tag);
          });
        }
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        decoration: BoxDecoration(
          color: isSelected
            ? const Color(0xFF6C5CE7).withValues(alpha: 0.3)
            : const Color(0xFF6C5CE7).withValues(alpha: 0.4), // Purple tint like in image
          borderRadius: BorderRadius.circular(20),
        ),
        child: Text(
          tag,
          style: TextStyle(
            color: Colors.white,
            fontSize: 14,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
    );
  }



  Widget _buildStoryTab() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20.0),
      child: Column(
        children: [
          // Title input field
          Container(
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(15),
              border: Border.all(
                color: Colors.white.withValues(alpha: 0.2),
                width: 1,
              ),
            ),
            child: TextField(
              controller: _titleController,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 16,
              ),
              decoration: const InputDecoration(
                hintText: 'Add a title',
                hintStyle: TextStyle(
                  color: Colors.white54,
                  fontSize: 16,
                ),
                border: InputBorder.none,
                contentPadding: EdgeInsets.all(20),
              ),
            ),
          ),

          const SizedBox(height: 20),

          // Dream content textarea - Smaller fixed height with scroll
          Container(
            height: 200, // Reduced height from 300 to 200
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(15),
              border: Border.all(
                color: Colors.white.withValues(alpha: 0.2),
                width: 1,
              ),
            ),
            child: TextField(
              controller: _dreamController,
              maxLines: null,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 16,
                height: 1.5,
              ),
              decoration: const InputDecoration(
                hintText: 'Write your dream here...',
                hintStyle: TextStyle(
                  color: Colors.white54,
                  fontSize: 16,
                ),
                border: InputBorder.none,
                contentPadding: EdgeInsets.all(20),
              ),
            ),
          ),

          const SizedBox(height: 20),

          // Voice dictation button
          AnimatedOpacity(
            opacity: _isVoicePressed ? 1.0 : 0.7,
            duration: const Duration(milliseconds: 150),
            child: GestureDetector(
              onTapDown: (_) => setState(() => _isVoicePressed = true),
              onTapUp: (_) => setState(() => _isVoicePressed = false),
              onTapCancel: () => setState(() => _isVoicePressed = false),
              onTap: _listen,
              child: Container(
                width: double.infinity,
                padding: const EdgeInsets.symmetric(vertical: 18),
                decoration: BoxDecoration(
                  color: _isListening
                    ? const Color(0xFF6C5CE7).withValues(alpha: 0.3)
                    : Colors.white.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(15),
                  border: Border.all(
                    color: _isListening
                      ? const Color(0xFF6C5CE7)
                      : Colors.white.withValues(alpha: 0.2),
                    width: 1,
                  ),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      _isListening ? Icons.mic : Icons.mic_none,
                      color: _isListening ? const Color(0xFF6C5CE7) : Colors.white70,
                      size: 24,
                    ),
                    const SizedBox(width: 12),
                    Text(
                      _isListening
                        ? 'Listening... Tap to stop'
                        : 'Write using voice dictation',
                      style: TextStyle(
                        color: _isListening ? const Color(0xFF6C5CE7) : Colors.white70,
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),

          const SizedBox(height: 40), // Extra space at bottom
        ],
      ),
    );
  }

  Widget _buildDetailsTab() {
    return Padding(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Date Section
          _buildDateSection(),
          const SizedBox(height: 20),

          // Type Section
          _buildTypeSection(),
          const SizedBox(height: 20),

          // Rate Section
          _buildRateSection(),
          const SizedBox(height: 20),

          // Dream Length Section
          _buildDreamLengthSection(),
          const SizedBox(height: 20),

          // Sleep Quality Section
          _buildSleepQualitySection(),
          const SizedBox(height: 20),

          // Yes/No Questions
          _buildYesNoQuestion('Individually in the dream', _individuallyInDream, (value) {
            setState(() {
              _individuallyInDream = value;
            });
          }),
          const SizedBox(height: 15),

          _buildYesNoQuestion('Matches the mood', _matchesTheMood, (value) {
            setState(() {
              _matchesTheMood = value;
            });
          }),
          const SizedBox(height: 15),

          _buildYesNoQuestion('People', _people, (value) {
            setState(() {
              _people = value;
            });
          }),
          const SizedBox(height: 15),

          _buildYesNoQuestion('Melody', _melody, (value) {
            setState(() {
              _melody = value;
            });
          }),
          const SizedBox(height: 15),

          _buildYesNoQuestion('Dreams and sexuality', _dreamsAndSexuality, (value) {
            setState(() {
              _dreamsAndSexuality = value;
            });
          }),
          const SizedBox(height: 15),

          _buildYesNoQuestion('Medication or alcohol', _medicationOrAlcohol, (value) {
            setState(() {
              _medicationOrAlcohol = value;
            });
          }),
          const SizedBox(height: 15),

          _buildYesNoQuestion('Advertisements or brands', _advertisementsOrBrands, (value) {
            setState(() {
              _advertisementsOrBrands = value;
            });
          }),
          const SizedBox(height: 20),

          // Linked to a dream context
          _buildYesNoQuestion('Linked to a dream context', _linkedToDreamContext, (value) {
            setState(() {
              _linkedToDreamContext = value;
            });
          }),
          const SizedBox(height: 15),

          // Context text field (only show if linked to dream context is Yes)
          if (_linkedToDreamContext == true) ...[
            _buildContextTextField(),
            const SizedBox(height: 20),
          ],

          // Recurring dream
          _buildYesNoQuestion('Recurring dream', _recurringDream, (value) {
            setState(() {
              _recurringDream = value;
            });
          }),
          const SizedBox(height: 20),

          // Notes section
          _buildNotesSection(),
          const SizedBox(height: 100), // Extra space for bottom navigation
        ],
      ),
    );
  }

  Widget _buildDateSection() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: const Color(0xFF6C5CE7).withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(15),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: const Color(0xFF6C5CE7),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.calendar_today,
                  color: Colors.white,
                  size: 20,
                ),
              ),
              const SizedBox(width: 15),
              const Text(
                'Date',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const Spacer(),
              GestureDetector(
                onTap: () => _selectDate(),
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 8),
                  decoration: BoxDecoration(
                    color: const Color(0xFF6C5CE7),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        _selectedDate != null
                          ? '${_selectedDate!.day} ${_getMonthName(_selectedDate!.month)} ${_selectedDate!.year}'
                          : 'Select Date',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      const SizedBox(width: 5),
                      const Icon(
                        Icons.keyboard_arrow_down,
                        color: Colors.white,
                        size: 16,
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: const Color(0xFF6C5CE7).withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(10),
              border: Border.all(
                color: const Color(0xFF6C5CE7).withValues(alpha: 0.3),
                width: 1,
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.info_outline,
                  color: const Color(0xFF6C5CE7),
                  size: 16,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'Only dreams from the last 7 days can be selected for better dream matching performance',
                    style: TextStyle(
                      color: Colors.white.withValues(alpha: 0.8),
                      fontSize: 12,
                      height: 1.3,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }


  Widget _buildTypeSection() {
    final dreamTypes = [
      {'name': 'Dream', 'icon': 'assets/images/pyramid.png'},
      {'name': 'Nightmare', 'icon': 'assets/images/triangle(1).png'},
      {'name': 'Lucid', 'icon': 'assets/images/triangle(2).png'},
      {'name': 'Prophetic', 'icon': 'assets/images/magic-ball.png'},
    ];

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: const Color(0xFF6C5CE7).withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(15),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Type',
            style: TextStyle(
              color: Colors.white,
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 15),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: dreamTypes.map((type) {
              final isSelected = _selectedDreamType == type['name'];
              return GestureDetector(
                onTap: () {
                  setState(() {
                    _selectedDreamType = type['name']!;
                  });
                },
                child: Container(
                  width: 70,
                  height: 70,
                  decoration: BoxDecoration(
                    color: isSelected
                      ? Colors.white
                      : Colors.white.withValues(alpha: 0.3),
                    borderRadius: BorderRadius.circular(15),
                  ),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Image.asset(
                        type['icon']!,
                        width: 24,
                        height: 24,
                        color: isSelected ? Colors.black : Colors.white,
                      ),
                      const SizedBox(height: 4),
                      Text(
                        type['name']!,
                        style: TextStyle(
                          color: isSelected ? Colors.black : Colors.white,
                          fontSize: 10,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
              );
            }).toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildRateSection() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: const Color(0xFF6C5CE7).withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(15),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Text(
                'Rate',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const Spacer(),
              Container(
                padding: const EdgeInsets.all(4),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Icon(
                  Icons.help_outline,
                  color: Colors.white70,
                  size: 16,
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          SliderTheme(
            data: SliderTheme.of(context).copyWith(
              activeTrackColor: const Color(0xFF00D4FF),
              inactiveTrackColor: Colors.white.withValues(alpha: 0.3),
              thumbColor: const Color(0xFF2A2A3E),
              overlayColor: const Color(0xFF00D4FF).withValues(alpha: 0.2),
              trackHeight: 6,
              thumbShape: const RoundSliderThumbShape(enabledThumbRadius: 12),
            ),
            child: Slider(
              value: _dreamRate,
              min: 0,
              max: 100,
              onChanged: (value) {
                setState(() {
                  _dreamRate = value;
                });
              },
            ),
          ),
          const SizedBox(height: 10),
          const Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Very bad',
                style: TextStyle(color: Colors.white70, fontSize: 12),
              ),
              Text(
                '0',
                style: TextStyle(color: Colors.white70, fontSize: 12),
              ),
              Text(
                '20',
                style: TextStyle(color: Colors.white70, fontSize: 12),
              ),
              Text(
                '40',
                style: TextStyle(color: Colors.white70, fontSize: 12),
              ),
              Text(
                '60',
                style: TextStyle(color: Colors.white70, fontSize: 12),
              ),
              Text(
                '80',
                style: TextStyle(color: Colors.white70, fontSize: 12),
              ),
              Text(
                '100',
                style: TextStyle(color: Colors.white70, fontSize: 12),
              ),
              Text(
                'Very good',
                style: TextStyle(color: Colors.white70, fontSize: 12),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildDreamLengthSection() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: const Color(0xFF6C5CE7).withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(15),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Text(
                'Dream length',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const Spacer(),
              Container(
                padding: const EdgeInsets.all(4),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Icon(
                  Icons.help_outline,
                  color: Colors.white70,
                  size: 16,
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          SliderTheme(
            data: SliderTheme.of(context).copyWith(
              activeTrackColor: const Color(0xFF00FF88),
              inactiveTrackColor: Colors.white.withValues(alpha: 0.3),
              thumbColor: const Color(0xFF2A2A3E),
              overlayColor: const Color(0xFF00FF88).withValues(alpha: 0.2),
              trackHeight: 6,
              thumbShape: const RoundSliderThumbShape(enabledThumbRadius: 12),
            ),
            child: Slider(
              value: _dreamLength,
              min: 0,
              max: 100,
              onChanged: (value) {
                setState(() {
                  _dreamLength = value;
                });
              },
            ),
          ),
          const SizedBox(height: 10),
          const Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Very bad',
                style: TextStyle(color: Colors.white70, fontSize: 12),
              ),
              Text(
                '0',
                style: TextStyle(color: Colors.white70, fontSize: 12),
              ),
              Text(
                '20',
                style: TextStyle(color: Colors.white70, fontSize: 12),
              ),
              Text(
                '40',
                style: TextStyle(color: Colors.white70, fontSize: 12),
              ),
              Text(
                '60',
                style: TextStyle(color: Colors.white70, fontSize: 12),
              ),
              Text(
                '80',
                style: TextStyle(color: Colors.white70, fontSize: 12),
              ),
              Text(
                '100',
                style: TextStyle(color: Colors.white70, fontSize: 12),
              ),
              Text(
                'Very good',
                style: TextStyle(color: Colors.white70, fontSize: 12),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSleepQualitySection() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: const Color(0xFF6C5CE7).withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(15),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Text(
                'Sleep quality',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const Spacer(),
              Container(
                padding: const EdgeInsets.all(4),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Icon(
                  Icons.help_outline,
                  color: Colors.white70,
                  size: 16,
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          SliderTheme(
            data: SliderTheme.of(context).copyWith(
              activeTrackColor: const Color(0xFF9B59B6),
              inactiveTrackColor: Colors.white.withValues(alpha: 0.3),
              thumbColor: const Color(0xFF2A2A3E),
              overlayColor: const Color(0xFF9B59B6).withValues(alpha: 0.2),
              trackHeight: 6,
              thumbShape: const RoundSliderThumbShape(enabledThumbRadius: 12),
            ),
            child: Slider(
              value: _sleepQuality,
              min: 0,
              max: 100,
              onChanged: (value) {
                setState(() {
                  _sleepQuality = value;
                });
              },
            ),
          ),
          const SizedBox(height: 10),
          const Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Very bad',
                style: TextStyle(color: Colors.white70, fontSize: 12),
              ),
              Text(
                '0',
                style: TextStyle(color: Colors.white70, fontSize: 12),
              ),
              Text(
                '20',
                style: TextStyle(color: Colors.white70, fontSize: 12),
              ),
              Text(
                '40',
                style: TextStyle(color: Colors.white70, fontSize: 12),
              ),
              Text(
                '60',
                style: TextStyle(color: Colors.white70, fontSize: 12),
              ),
              Text(
                '80',
                style: TextStyle(color: Colors.white70, fontSize: 12),
              ),
              Text(
                '100',
                style: TextStyle(color: Colors.white70, fontSize: 12),
              ),
              Text(
                'Very good',
                style: TextStyle(color: Colors.white70, fontSize: 12),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Future<void> _selectDate() async {
    final DateTime now = DateTime.now();
    final DateTime oneWeekAgo = now.subtract(const Duration(days: 7));

    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate ?? now,
      firstDate: oneWeekAgo, // Son 1 hafta
      lastDate: now,
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: const ColorScheme.dark(
              primary: Color(0xFF6C5CE7),
              onPrimary: Colors.white,
              surface: Color(0xFF2A2A3E),
              onSurface: Colors.white,
            ),
          ),
          child: child!,
        );
      },
    );
    if (picked != null && picked != _selectedDate) {
      setState(() {
        _selectedDate = picked;
      });
    }
  }



  String _getMonthName(int month) {
    const months = [
      'January', 'February', 'March', 'April', 'May', 'June',
      'July', 'August', 'September', 'October', 'November', 'December'
    ];
    return months[month - 1];
  }

  Widget _buildYesNoQuestion(String title, bool? currentValue, Function(bool) onChanged) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: const Color(0xFF6C5CE7).withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(15),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                title,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const Spacer(),
              Container(
                padding: const EdgeInsets.all(4),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Icon(
                  Icons.help_outline,
                  color: Colors.white70,
                  size: 16,
                ),
              ),
            ],
          ),
          const SizedBox(height: 15),
          Row(
            children: [
              Expanded(
                child: GestureDetector(
                  onTap: () => onChanged(false),
                  child: Container(
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    decoration: BoxDecoration(
                      color: currentValue == false
                        ? Colors.white
                        : const Color(0xFF6C5CE7).withValues(alpha: 0.5),
                      borderRadius: BorderRadius.circular(25),
                    ),
                    child: Center(
                      child: Text(
                        'No',
                        style: TextStyle(
                          color: currentValue == false ? Colors.black : Colors.white,
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 10),
              Expanded(
                child: GestureDetector(
                  onTap: () => onChanged(true),
                  child: Container(
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    decoration: BoxDecoration(
                      color: currentValue == true
                        ? Colors.white
                        : const Color(0xFF6C5CE7).withValues(alpha: 0.5),
                      borderRadius: BorderRadius.circular(25),
                    ),
                    child: Center(
                      child: Text(
                        'Yes',
                        style: TextStyle(
                          color: currentValue == true ? Colors.black : Colors.white,
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildContextTextField() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: const Color(0xFF6C5CE7).withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(15),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'What is the context',
            style: TextStyle(
              color: Colors.white,
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 15),
          Container(
            height: 120,
            padding: const EdgeInsets.all(15),
            decoration: BoxDecoration(
              color: const Color(0xFF6C5CE7).withValues(alpha: 0.5),
              borderRadius: BorderRadius.circular(12),
            ),
            child: TextField(
              controller: _contextController,
              maxLines: null,
              expands: true,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 14,
              ),
              decoration: const InputDecoration(
                border: InputBorder.none,
                hintText: 'Describe the context...',
                hintStyle: TextStyle(
                  color: Colors.white60,
                  fontSize: 14,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNotesSection() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: const Color(0xFF6C5CE7).withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(15),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Text(
                'Notes',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const Spacer(),
              Container(
                padding: const EdgeInsets.all(4),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Icon(
                  Icons.help_outline,
                  color: Colors.white70,
                  size: 16,
                ),
              ),
            ],
          ),
          const SizedBox(height: 15),
          Container(
            height: 120,
            padding: const EdgeInsets.all(15),
            decoration: BoxDecoration(
              color: const Color(0xFF6C5CE7).withValues(alpha: 0.5),
              borderRadius: BorderRadius.circular(12),
            ),
            child: TextField(
              controller: _notesController,
              maxLines: null,
              expands: true,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 14,
              ),
              decoration: const InputDecoration(
                border: InputBorder.none,
                hintText: 'Write down anything else you want about your dream',
                hintStyle: TextStyle(
                  color: Colors.white60,
                  fontSize: 14,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  bool _validateStoryTab() {
    if (_titleController.text.trim().isEmpty) {
      _showErrorDialog('Please enter a dream title');
      return false;
    }
    if (_dreamController.text.trim().isEmpty) {
      _showErrorDialog('Please describe your dream');
      return false;
    }
    if (_selectedDreamType == null) {
      _showErrorDialog('Please select a dream type');
      return false;
    }
    if (_selectedDate == null) {
      _showErrorDialog('Please select a date');
      return false;
    }
    // Sleep quality is always set (has default value), so no validation needed
    return true;
  }

  bool _validateDetailsTab() {
    if (_individuallyInDream == null) {
      _showErrorDialog('Please answer "Individually in the dream"');
      return false;
    }
    if (_matchesTheMood == null) {
      _showErrorDialog('Please answer "Matches the mood"');
      return false;
    }
    if (_people == null) {
      _showErrorDialog('Please answer "People"');
      return false;
    }
    if (_melody == null) {
      _showErrorDialog('Please answer "Melody"');
      return false;
    }
    if (_dreamsAndSexuality == null) {
      _showErrorDialog('Please answer "Dreams and sexuality"');
      return false;
    }
    if (_medicationOrAlcohol == null) {
      _showErrorDialog('Please answer "Medication or alcohol"');
      return false;
    }
    if (_advertisementsOrBrands == null) {
      _showErrorDialog('Please answer "Advertisements or brands"');
      return false;
    }
    if (_linkedToDreamContext == null) {
      _showErrorDialog('Please answer "Linked to a dream context"');
      return false;
    }
    if (_linkedToDreamContext == true && _contextController.text.trim().isEmpty) {
      _showErrorDialog('Please describe the dream context');
      return false;
    }
    if (_recurringDream == null) {
      _showErrorDialog('Please answer "Recurring dream"');
      return false;
    }
    return true;
  }

  bool _validateSymbolsTab() {
    if (_selectedTags.isEmpty) {
      _showErrorDialog('Please select at least one symbol/tag');
      return false;
    }
    return true;
  }

  void _showErrorDialog(String message) {
    // Determine dialog title based on message content
    String title = 'Error';
    IconData icon = Icons.error_outline;
    Color iconColor = Colors.red;

    if (message.contains('internet') || message.contains('network') || message.contains('connection')) {
      title = 'Connection Error';
      icon = Icons.wifi_off;
      iconColor = Colors.orange;
    } else if (message.contains('Please')) {
      title = 'Incomplete Form';
      icon = Icons.warning_outlined;
      iconColor = Colors.amber;
    } else if (message.contains('permission')) {
      title = 'Permission Error';
      icon = Icons.lock_outline;
      iconColor = Colors.red;
    }

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: const Color(0xFF2D1B69),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          title: Row(
            children: [
              Icon(icon, color: iconColor, size: 24),
              const SizedBox(width: 10),
              Text(
                title,
                style: const TextStyle(color: Colors.white, fontSize: 18),
              ),
            ],
          ),
          content: Text(
            message,
            style: const TextStyle(color: Colors.white70, fontSize: 16),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              style: TextButton.styleFrom(
                backgroundColor: const Color(0xFF6C5CE7),
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
              ),
              child: const Text('OK'),
            ),
          ],
        );
      },
    );
  }

  Future<void> _saveDream() async {
    // Validate all tabs
    if (!_validateStoryTab()) {
      _tabController.animateTo(0); // Go to Story tab
      return;
    }
    if (!_validateDetailsTab()) {
      _tabController.animateTo(1); // Go to Details tab
      return;
    }
    if (!_validateSymbolsTab()) {
      _tabController.animateTo(2); // Go to Symbols tab
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) {
        _showErrorDialog('You must be logged in to save dreams');
        return;
      }

      // Create dream data
      final dreamData = {
        'userId': user.uid,
        'title': _titleController.text.trim(),
        'description': _dreamController.text.trim(),
        'dreamType': _selectedDreamType,
        'date': Timestamp.fromDate(_selectedDate!),
        'sleepQuality': _sleepQuality.round(),
        'individuallyInDream': _individuallyInDream,
        'matchesTheMood': _matchesTheMood,
        'people': _people,
        'melody': _melody,
        'dreamsAndSexuality': _dreamsAndSexuality,
        'medicationOrAlcohol': _medicationOrAlcohol,
        'advertisementsOrBrands': _advertisementsOrBrands,
        'linkedToDreamContext': _linkedToDreamContext,
        'dreamContext': _linkedToDreamContext == true ? _contextController.text.trim() : null,
        'recurringDream': _recurringDream,
        'notes': _notesController.text.trim().isNotEmpty ? _notesController.text.trim() : null,
        'tags': _selectedTags,
        'createdAt': FieldValue.serverTimestamp(),
      };

      // Save to dreams collection with timeout
      final dreamRef = await FirebaseFirestore.instance
          .collection('dreams')
          .add(dreamData)
          .timeout(
            const Duration(seconds: 30),
            onTimeout: () => throw Exception('Connection timeout. Please check your internet connection.'),
          );

      // Update user's dreams array with timeout
      await FirebaseFirestore.instance
          .collection('users')
          .doc(user.uid)
          .update({
        'dreams': FieldValue.arrayUnion([dreamRef.id])
      }).timeout(
        const Duration(seconds: 15),
        onTimeout: () => throw Exception('Connection timeout while updating user profile.'),
      );

      // Start dream matching process in background (don't wait for this)
      DreamProcessingService.processDreamMatching(dreamRef.id).catchError((e) {
        print('Dream matching failed: $e');
        // Don't show error to user, matching can be done later
      });

      // Show success dialog
      if (mounted) {
        _showSuccessDialog();
      }
    } catch (e) {
      String errorMessage;
      print('Save dream error: $e'); // Debug log

      if (e.toString().contains('Unable to resolve host') ||
          e.toString().contains('firestore.googleapis.com') ||
          e.toString().contains('UNAVAILABLE') ||
          e.toString().contains('network') ||
          e.toString().contains('timeout')) {
        errorMessage = 'Connection problem. Please check your internet connection and try again.';
      } else if (e.toString().contains('permission') || e.toString().contains('PERMISSION_DENIED')) {
        errorMessage = 'Permission denied. Please check your account permissions.';
      } else if (e.toString().contains('UNAUTHENTICATED')) {
        errorMessage = 'Authentication error. Please sign in again.';
      } else {
        errorMessage = 'Failed to save dream. Please try again.';
      }
      _showErrorDialog(errorMessage);
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _showSuccessDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: const Color(0xFF2D1B69),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(25),
          ),
          contentPadding: const EdgeInsets.all(30),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Success icon with animation
              Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  color: const Color(0xFF6C5CE7).withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(40),
                  border: Border.all(
                    color: const Color(0xFF6C5CE7),
                    width: 2,
                  ),
                ),
                child: const Icon(
                  Icons.check_circle_outline,
                  color: Color(0xFF6C5CE7),
                  size: 40,
                ),
              ),
              const SizedBox(height: 25),

              // Title
              const Text(
                'Dream Shared Successfully!',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 22,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 15),

              // Subtitle
              const Text(
                'How exciting!',
                style: TextStyle(
                  color: Color(0xFF6C5CE7),
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 20),

              // Description
              const Text(
                'Your dream has been safely stored in our mystical vault. You can now explore AI-powered interpretations and discover matching profiles with similar dreams.',
                style: TextStyle(
                  color: Colors.white70,
                  fontSize: 14,
                  height: 1.5,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 20),

              // Info box
              Container(
                padding: const EdgeInsets.all(15),
                decoration: BoxDecoration(
                  color: const Color(0xFF6C5CE7).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(15),
                  border: Border.all(
                    color: const Color(0xFF6C5CE7).withValues(alpha: 0.3),
                    width: 1,
                  ),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.info_outline,
                      color: const Color(0xFF6C5CE7),
                      size: 20,
                    ),
                    const SizedBox(width: 10),
                    Expanded(
                      child: Text(
                        'Visit your dream details to get AI interpretations and explore the Dream Records section for matching profiles.',
                        style: TextStyle(
                          color: Colors.white.withValues(alpha: 0.9),
                          fontSize: 12,
                          height: 1.4,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 30),

              // Action buttons
              Row(
                children: [
                  Expanded(
                    child: TextButton(
                      onPressed: () {
                        Navigator.of(context).pop(); // Close dialog
                        Navigator.of(context).pop(); // Go back to home
                      },
                      style: TextButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(15),
                          side: BorderSide(
                            color: Colors.white.withValues(alpha: 0.3),
                            width: 1,
                          ),
                        ),
                      ),
                      child: const Text(
                        'Back to Home',
                        style: TextStyle(
                          color: Colors.white70,
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 15),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: () {
                        Navigator.of(context).pop(); // Close dialog
                        Navigator.of(context).pushReplacement(
                          MaterialPageRoute(
                            builder: (context) => const DreamRecordsScreen(),
                          ),
                        );
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: const Color(0xFF6C5CE7),
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(15),
                        ),
                        elevation: 0,
                      ),
                      child: const Text(
                        'View Records',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }
}
