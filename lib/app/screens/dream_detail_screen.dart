import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../../services/dream_processing_service.dart';
import '../widgets/dream_detail/dream_header_widget.dart';
import '../widgets/dream_detail/dream_info_widget.dart';
import '../widgets/dream_detail/dream_description_widget.dart';
import '../widgets/dream_detail/dream_interpretation_widget.dart';
import '../widgets/dream_detail/dream_matches_widget.dart';
import 'blog_screen.dart';
import 'statistics_screen.dart';
import '../widgets/custom_bottom_navigation.dart';

class DreamDetailScreen extends StatefulWidget {
  final String dreamId;
  final Map<String, dynamic> dreamData;

  const DreamDetailScreen({
    super.key,
    required this.dreamId,
    required this.dreamData,
  });

  @override
  State<DreamDetailScreen> createState() => _DreamDetailScreenState();
}

class _DreamDetailScreenState extends State<DreamDetailScreen> {
  int _selectedIndex = 1; // Feather icon is selected

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        image: DecorationImage(
          image: AssetImage('assets/images/bg.jpg'),
          fit: BoxFit.cover,
        ),
      ),
      child: Scaffold(
        backgroundColor: Colors.transparent,
        body: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                Colors.black.withValues(alpha: 0.4),
                Colors.black.withValues(alpha: 0.6),
              ],
            ),
          ),
          child: SafeArea(
            child: Column(
              children: [
                // Header
                DreamHeaderWidget(
                  title: widget.dreamData['title'] ?? 'Dream Detail',
                  onBackPressed: () => Navigator.pop(context),
                ),
                
                // Dream content with real-time interpretation updates
                Expanded(
                  child: StreamBuilder<QuerySnapshot>(
                    stream: DreamProcessingService.getInterpretationStream(widget.dreamId),
                    builder: (context, interpretationSnapshot) {
                      final interpretationDoc = interpretationSnapshot.hasData &&
                          interpretationSnapshot.data!.docs.isNotEmpty
                        ? interpretationSnapshot.data!.docs.first
                        : null;

                      return SingleChildScrollView(
                        padding: const EdgeInsets.symmetric(horizontal: 20),
                        child: _buildDreamContent(widget.dreamData, interpretationDoc),
                      );
                    },
                  ),
                ),
              ],
            ),
          ),
        ),
        bottomNavigationBar: CustomBottomNavigation(
          currentIndex: 1, // Dream detail is part of dream records
          context: context,
        ),
      ),
    );
  }

  Widget _buildDreamContent(Map<String, dynamic> data, DocumentSnapshot? interpretationDoc) {
    final description = data['description'] ?? '';
    final dreamType = data['dreamType'] ?? 'Dream';
    final date = (data['date'] as Timestamp?)?.toDate() ?? DateTime.now();
    final tags = List<String>.from(data['tags'] ?? []);
    final sleepQuality = data['sleepQuality']?.toDouble() ?? 50.0;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Dream info widget
        DreamInfoWidget(
          dreamType: dreamType,
          date: date,
          sleepQuality: sleepQuality,
        ),

        // Dream description widget
        DreamDescriptionWidget(
          description: description,
          tags: tags,
        ),

        // Dream interpretation widget
        DreamInterpretationWidget(
          dreamId: widget.dreamId,
          interpretationDoc: interpretationDoc,
        ),

        // Dream matches widget
        DreamMatchesWidget(dreamId: widget.dreamId),

        const SizedBox(height: 100), // Extra space for bottom navigation
      ],
    );
  }

  // Bottom navigation bar
  Widget _buildBottomNavigationBar() {
    return Container(
      height: 80,
      decoration: const BoxDecoration(
        color: Colors.black,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          _buildBottomNavItem(0, 'assets/images/dreamdeus.png'),
          _buildBottomNavItem(1, 'assets/images/feather.png'),
          _buildBottomNavItem(2, 'assets/images/rainbow.png'),
          _buildBottomNavItem(3, 'assets/images/triangle.png'),
        ],
      ),
    );
  }

  Widget _buildBottomNavItem(int index, String iconPath) {
    final isSelected = _selectedIndex == index;
    return GestureDetector(
      onTap: () => _onBottomNavTap(index),
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 20),
        child: Image.asset(
          iconPath,
          height: 32,
          width: 32,
          color: isSelected ? const Color(0xFF6C5CE7) : Colors.white54,
        ),
      ),
    );
  }

  void _onBottomNavTap(int index) {
    setState(() {
      _selectedIndex = index;
    });

    switch (index) {
      case 0:
        // Home
        Navigator.popUntil(context, (route) => route.isFirst);
        break;
      case 1:
        // Dream Records - go back to records
        Navigator.pop(context);
        break;
      case 2:
        // Statistics
        Navigator.push(
          context,
          MaterialPageRoute(builder: (context) => const StatisticsScreen()),
        );
        break;
      case 3:
        // Blog
        Navigator.push(
          context,
          MaterialPageRoute(builder: (context) => const BlogScreen()),
        );
        break;
    }
  }
}
