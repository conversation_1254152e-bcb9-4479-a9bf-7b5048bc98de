import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'dart:math' as math;

class DreamOrbWidget extends StatefulWidget {
  final double value;
  final String title;
  final Color primaryColor;
  final Color secondaryColor;
  final double size;

  const DreamOrbWidget({
    super.key,
    required this.value,
    required this.title,
    required this.primaryColor,
    required this.secondaryColor,
    this.size = 120,
  });

  @override
  State<DreamOrbWidget> createState() => _DreamOrbWidgetState();
}

class _DreamOrbWidgetState extends State<DreamOrbWidget>
    with TickerProviderStateMixin {
  late AnimationController _rotationController;
  late AnimationController _pulseController;
  late AnimationController _particleController;

  @override
  void initState() {
    super.initState();
    _rotationController = AnimationController(
      duration: const Duration(seconds: 8),
      vsync: this,
    )..repeat();

    _pulseController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    )..repeat(reverse: true);

    _particleController = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    )..repeat();
  }

  @override
  void dispose() {
    _rotationController.dispose();
    _pulseController.dispose();
    _particleController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: widget.size,
      height: widget.size,
      child: Stack(
        alignment: Alignment.center,
        children: [
          // Outer glow effect
          AnimatedBuilder(
            animation: _pulseController,
            builder: (context, child) {
              return Container(
                width: widget.size + (_pulseController.value * 20),
                height: widget.size + (_pulseController.value * 20),
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  gradient: RadialGradient(
                    colors: [
                      widget.primaryColor.withValues(alpha: 0.3 * (1 - _pulseController.value)),
                      Colors.transparent,
                    ],
                  ),
                ),
              );
            },
          ),

          // Particle field
          AnimatedBuilder(
            animation: _particleController,
            builder: (context, child) {
              return CustomPaint(
                size: Size(widget.size, widget.size),
                painter: ParticleFieldPainter(
                  animation: _particleController.value,
                  color: widget.primaryColor,
                ),
              );
            },
          ),

          // Main orb
          AnimatedBuilder(
            animation: _rotationController,
            builder: (context, child) {
              return Transform.rotate(
                angle: _rotationController.value * 2 * math.pi,
                child: Container(
                  width: widget.size * 0.8,
                  height: widget.size * 0.8,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        widget.primaryColor,
                        widget.secondaryColor,
                        widget.primaryColor.withValues(alpha: 0.7),
                      ],
                      stops: const [0.0, 0.5, 1.0],
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: widget.primaryColor.withValues(alpha: 0.5),
                        blurRadius: 20,
                        spreadRadius: 5,
                      ),
                    ],
                  ),
                  child: Stack(
                    children: [
                      // Inner highlight
                      Positioned(
                        top: widget.size * 0.1,
                        left: widget.size * 0.1,
                        child: Container(
                          width: widget.size * 0.3,
                          height: widget.size * 0.3,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            gradient: RadialGradient(
                              colors: [
                                Colors.white.withValues(alpha: 0.6),
                                Colors.transparent,
                              ],
                            ),
                          ),
                        ),
                      ),
                      // Progress indicator
                      Center(
                        child: SizedBox(
                          width: widget.size * 0.6,
                          height: widget.size * 0.6,
                          child: CircularProgressIndicator(
                            value: widget.value / 100,
                            strokeWidth: 3,
                            backgroundColor: Colors.white.withValues(alpha: 0.2),
                            valueColor: AlwaysStoppedAnimation<Color>(
                              Colors.white.withValues(alpha: 0.8),
                            ),
                          ),
                        ),
                      ),
                      // Value text
                      Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              '${widget.value.toInt()}%',
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            Text(
                              widget.title,
                              style: TextStyle(
                                color: Colors.white.withValues(alpha: 0.8),
                                fontSize: 10,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
        ],
      ),
    );
  }
}

class ParticleFieldPainter extends CustomPainter {
  final double animation;
  final Color color;

  ParticleFieldPainter({required this.animation, required this.color});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color.withValues(alpha: 0.6)
      ..style = PaintingStyle.fill;

    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width / 2;

    // Draw floating particles
    for (int i = 0; i < 12; i++) {
      final angle = (i * 30 + animation * 360) * math.pi / 180;
      final particleRadius = radius * 0.7 + math.sin(animation * 2 * math.pi + i) * 10;
      
      final x = center.dx + math.cos(angle) * particleRadius;
      final y = center.dy + math.sin(angle) * particleRadius;
      
      final particleSize = 2 + math.sin(animation * 4 * math.pi + i) * 1;
      
      canvas.drawCircle(
        Offset(x, y),
        particleSize,
        paint..color = color.withValues(alpha: 0.4 + math.sin(animation * 2 * math.pi + i) * 0.3),
      );
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
