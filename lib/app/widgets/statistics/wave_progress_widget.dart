import 'package:flutter/material.dart';
import 'dart:math' as math;

class WaveProgressWidget extends StatefulWidget {
  final double progress;
  final String title;
  final String value;
  final Color waveColor;
  final Color backgroundColor;
  final double height;

  const WaveProgressWidget({
    super.key,
    required this.progress,
    required this.title,
    required this.value,
    required this.waveColor,
    required this.backgroundColor,
    this.height = 100,
  });

  @override
  State<WaveProgressWidget> createState() => _WaveProgressWidgetState();
}

class _WaveProgressWidgetState extends State<WaveProgressWidget>
    with TickerProviderStateMixin {
  late AnimationController _waveController;
  late AnimationController _progressController;

  @override
  void initState() {
    super.initState();
    _waveController = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    )..repeat();

    _progressController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _progressController.forward();
  }

  @override
  void dispose() {
    _waveController.dispose();
    _progressController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: widget.height,
      decoration: BoxDecoration(
        color: widget.backgroundColor,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: widget.waveColor.withValues(alpha: 0.3),
            blurRadius: 15,
            spreadRadius: 2,
          ),
        ],
      ),
      child: Stack(
        children: [
          // Wave animation
          ClipRRect(
            borderRadius: BorderRadius.circular(16),
            child: AnimatedBuilder(
              animation: Listenable.merge([_waveController, _progressController]),
              builder: (context, child) {
                return CustomPaint(
                  size: Size(double.infinity, widget.height),
                  painter: WavePainter(
                    waveAnimation: _waveController.value,
                    progress: widget.progress * _progressController.value,
                    waveColor: widget.waveColor,
                  ),
                );
              },
            ),
          ),

          // Content overlay
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            child: Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.center,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        widget.title,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 13,
                          fontWeight: FontWeight.w500,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 2),
                      Text(
                        widget.value,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
                // Progress circle
                AnimatedBuilder(
                  animation: _progressController,
                  builder: (context, child) {
                    return SizedBox(
                      width: 40,
                      height: 40,
                      child: CircularProgressIndicator(
                        value: widget.progress * _progressController.value / 100,
                        strokeWidth: 3,
                        backgroundColor: Colors.white.withValues(alpha: 0.2),
                        valueColor: const AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class WavePainter extends CustomPainter {
  final double waveAnimation;
  final double progress;
  final Color waveColor;

  WavePainter({
    required this.waveAnimation,
    required this.progress,
    required this.waveColor,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = waveColor.withValues(alpha: 0.8)
      ..style = PaintingStyle.fill;

    final path = Path();
    final waveHeight = size.height * (progress / 100);
    final baseY = size.height - waveHeight;

    // Create wave path
    path.moveTo(0, size.height);
    path.lineTo(0, baseY);

    for (double x = 0; x <= size.width; x += 1) {
      final y = baseY + 
          math.sin((x / size.width * 4 * math.pi) + (waveAnimation * 2 * math.pi)) * 8 +
          math.sin((x / size.width * 2 * math.pi) + (waveAnimation * 3 * math.pi)) * 4;
      path.lineTo(x, y);
    }

    path.lineTo(size.width, size.height);
    path.close();

    canvas.drawPath(path, paint);

    // Add secondary wave for depth
    final secondaryPaint = Paint()
      ..color = waveColor.withValues(alpha: 0.4)
      ..style = PaintingStyle.fill;

    final secondaryPath = Path();
    secondaryPath.moveTo(0, size.height);
    secondaryPath.lineTo(0, baseY + 10);

    for (double x = 0; x <= size.width; x += 1) {
      final y = baseY + 10 + 
          math.sin((x / size.width * 3 * math.pi) + (waveAnimation * 2.5 * math.pi)) * 6;
      secondaryPath.lineTo(x, y);
    }

    secondaryPath.lineTo(size.width, size.height);
    secondaryPath.close();

    canvas.drawPath(secondaryPath, secondaryPaint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
