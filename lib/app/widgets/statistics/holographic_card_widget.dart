import 'package:flutter/material.dart';
import 'package:glassmorphism/glassmorphism.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'dart:math' as math;

class HolographicCardWidget extends StatefulWidget {
  final Widget child;
  final double width;
  final double height;
  final List<Color> gradientColors;

  const HolographicCardWidget({
    super.key,
    required this.child,
    required this.width,
    required this.height,
    required this.gradientColors,
  });

  @override
  State<HolographicCardWidget> createState() => _HolographicCardWidgetState();
}

class _HolographicCardWidgetState extends State<HolographicCardWidget>
    with TickerProviderStateMixin {
  late AnimationController _shimmerController;
  late AnimationController _rotationController;
  Offset? _localPosition;

  @override
  void initState() {
    super.initState();
    _shimmerController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    )..repeat();

    _rotationController = AnimationController(
      duration: const Duration(seconds: 10),
      vsync: this,
    )..repeat();
  }

  @override
  void dispose() {
    _shimmerController.dispose();
    _rotationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onHover: (event) {
        setState(() {
          _localPosition = event.localPosition;
        });
      },
      onExit: (event) {
        setState(() {
          _localPosition = null;
        });
      },
      child: GestureDetector(
        onPanUpdate: (details) {
          setState(() {
            _localPosition = details.localPosition;
          });
        },
        onPanEnd: (details) {
          setState(() {
            _localPosition = null;
          });
        },
        child: AnimatedBuilder(
          animation: _rotationController,
          builder: (context, child) {
            return Transform(
              alignment: Alignment.center,
              transform: Matrix4.identity()
                ..setEntry(3, 2, 0.001)
                ..rotateY(_getRotationY())
                ..rotateX(_getRotationX()),
              child: Container(
                width: widget.width,
                height: widget.height,
                child: Stack(
                  children: [
                    // Base holographic effect
                    GlassmorphicContainer(
                      width: widget.width,
                      height: widget.height,
                      borderRadius: 20,
                      blur: 20,
                      alignment: Alignment.bottomCenter,
                      border: 2,
                      linearGradient: LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: [
                          Colors.white.withValues(alpha: 0.1),
                          Colors.white.withValues(alpha: 0.05),
                        ],
                      ),
                      borderGradient: LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: widget.gradientColors.map((c) => c.withValues(alpha: 0.5)).toList(),
                      ),
                      child: Container(),
                    ),

                    // Shimmer effect
                    AnimatedBuilder(
                      animation: _shimmerController,
                      builder: (context, child) {
                        return Container(
                          width: widget.width,
                          height: widget.height,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(20),
                            gradient: LinearGradient(
                              begin: Alignment(-1.0 + _shimmerController.value * 2, -1.0),
                              end: Alignment(1.0 + _shimmerController.value * 2, 1.0),
                              colors: [
                                Colors.transparent,
                                Colors.white.withValues(alpha: 0.2),
                                Colors.transparent,
                              ],
                              stops: const [0.0, 0.5, 1.0],
                            ),
                          ),
                        );
                      },
                    ),

                    // Rainbow edge effect
                    Container(
                      width: widget.width,
                      height: widget.height,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(20),
                        border: Border.all(
                          width: 2,
                          color: Colors.transparent,
                        ),
                        gradient: LinearGradient(
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                          colors: widget.gradientColors,
                        ),
                      ),
                    ),

                    // Content
                    Container(
                      width: widget.width,
                      height: widget.height,
                      padding: const EdgeInsets.all(16),
                      child: widget.child,
                    ),

                    // Interactive light effect
                    if (_localPosition != null)
                      Positioned(
                        left: _localPosition!.dx - 50,
                        top: _localPosition!.dy - 50,
                        child: Container(
                          width: 100,
                          height: 100,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            gradient: RadialGradient(
                              colors: [
                                Colors.white.withValues(alpha: 0.3),
                                Colors.transparent,
                              ],
                            ),
                          ),
                        ),
                      ),
                  ],
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  double _getRotationY() {
    if (_localPosition == null) return 0;
    final center = widget.width / 2;
    final diff = (_localPosition!.dx - center) / center;
    return diff * 0.1; // Subtle rotation
  }

  double _getRotationX() {
    if (_localPosition == null) return 0;
    final center = widget.height / 2;
    final diff = (_localPosition!.dy - center) / center;
    return -diff * 0.1; // Subtle rotation
  }
}

class HolographicStatCard extends StatelessWidget {
  final String title;
  final String value;
  final IconData icon;
  final List<Color> gradientColors;

  const HolographicStatCard({
    super.key,
    required this.title,
    required this.value,
    required this.icon,
    required this.gradientColors,
  });

  @override
  Widget build(BuildContext context) {
    return HolographicCardWidget(
      width: 150,
      height: 100,
      gradientColors: gradientColors,
      child: LayoutBuilder(
        builder: (context, constraints) {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Container(
                padding: const EdgeInsets.all(4),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: gradientColors,
                  ),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Icon(
                  icon,
                  color: Colors.white,
                  size: 14,
                ),
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    value,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 2),
                  Text(
                    title,
                    style: TextStyle(
                      color: Colors.white.withValues(alpha: 0.8),
                      fontSize: 9,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ],
          );
        },
      ),
    ).animate()
      .fadeIn(duration: 600.ms, delay: 200.ms)
      .slideY(begin: 0.3, end: 0, duration: 600.ms, delay: 200.ms)
      .shimmer(duration: 2000.ms, delay: 1000.ms);
  }
}
