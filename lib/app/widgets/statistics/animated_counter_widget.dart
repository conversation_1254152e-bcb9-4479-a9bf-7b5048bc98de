import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';

class AnimatedCounterWidget extends StatefulWidget {
  final int targetValue;
  final String suffix;
  final TextStyle? textStyle;
  final Duration duration;
  final bool isKFormat;

  const AnimatedCounterWidget({
    super.key,
    required this.targetValue,
    this.suffix = '',
    this.textStyle,
    this.duration = const Duration(milliseconds: 1500),
    this.isKFormat = false,
  });

  @override
  State<AnimatedCounterWidget> createState() => _AnimatedCounterWidgetState();
}

class _AnimatedCounterWidgetState extends State<AnimatedCounterWidget>
    with TickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;
  int _currentValue = 0;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: widget.duration,
      vsync: this,
    );

    _animation = Tween<double>(
      begin: 0,
      end: widget.targetValue.toDouble(),
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeOutCubic,
    ));

    _animation.addListener(() {
      setState(() {
        _currentValue = _animation.value.round();
      });
    });

    _controller.forward();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  String _formatValue(int value) {
    if (widget.isKFormat && value >= 1000) {
      return '${(value / 1000).toStringAsFixed(0)}K';
    }
    return value.toString();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return Text(
          '${_formatValue(_currentValue)}${widget.suffix}',
          style: widget.textStyle ?? const TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        );
      },
    ).animate()
      .fadeIn(duration: 300.ms)
      .scale(begin: const Offset(0.8, 0.8), duration: 600.ms, curve: Curves.elasticOut);
  }
}

class PulsingIconWidget extends StatefulWidget {
  final IconData icon;
  final Color color;
  final double size;

  const PulsingIconWidget({
    super.key,
    required this.icon,
    required this.color,
    this.size = 24,
  });

  @override
  State<PulsingIconWidget> createState() => _PulsingIconWidgetState();
}

class _PulsingIconWidgetState extends State<PulsingIconWidget>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late Animation<double> _pulseAnimation;

  @override
  void initState() {
    super.initState();
    _pulseController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    )..repeat(reverse: true);

    _pulseAnimation = Tween<double>(
      begin: 0.8,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _pulseController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _pulseAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _pulseAnimation.value,
          child: Icon(
            widget.icon,
            color: widget.color,
            size: widget.size,
          ),
        );
      },
    );
  }
}

class GlowingTextWidget extends StatefulWidget {
  final String text;
  final TextStyle textStyle;
  final Color glowColor;

  const GlowingTextWidget({
    super.key,
    required this.text,
    required this.textStyle,
    required this.glowColor,
  });

  @override
  State<GlowingTextWidget> createState() => _GlowingTextWidgetState();
}

class _GlowingTextWidgetState extends State<GlowingTextWidget>
    with TickerProviderStateMixin {
  late AnimationController _glowController;
  late Animation<double> _glowAnimation;

  @override
  void initState() {
    super.initState();
    _glowController = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    )..repeat(reverse: true);

    _glowAnimation = Tween<double>(
      begin: 0.3,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _glowController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _glowController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _glowAnimation,
      builder: (context, child) {
        return Text(
          widget.text,
          style: widget.textStyle.copyWith(
            shadows: [
              Shadow(
                color: widget.glowColor.withValues(alpha: _glowAnimation.value),
                blurRadius: 10 * _glowAnimation.value,
              ),
              Shadow(
                color: widget.glowColor.withValues(alpha: _glowAnimation.value * 0.5),
                blurRadius: 20 * _glowAnimation.value,
              ),
            ],
          ),
        );
      },
    );
  }
}
