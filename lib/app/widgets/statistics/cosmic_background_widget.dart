import 'package:flutter/material.dart';
import 'dart:math' as math;

class CosmicBackgroundWidget extends StatefulWidget {
  final Widget child;

  const CosmicBackgroundWidget({
    super.key,
    required this.child,
  });

  @override
  State<CosmicBackgroundWidget> createState() => _CosmicBackgroundWidgetState();
}

class _CosmicBackgroundWidgetState extends State<CosmicBackgroundWidget>
    with TickerProviderStateMixin {
  late AnimationController _starsController;
  late AnimationController _nebulaController;
  late AnimationController _planetController;

  @override
  void initState() {
    super.initState();
    _starsController = AnimationController(
      duration: const Duration(seconds: 20),
      vsync: this,
    )..repeat();

    _nebulaController = AnimationController(
      duration: const Duration(seconds: 15),
      vsync: this,
    )..repeat();

    _planetController = AnimationController(
      duration: const Duration(seconds: 30),
      vsync: this,
    )..repeat();
  }

  @override
  void dispose() {
    _starsController.dispose();
    _nebulaController.dispose();
    _planetController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        // Base cosmic gradient
        Container(
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                Color(0xFF0F0C29),
                Color(0xFF24243e),
                Color(0xFF302B63),
                Color(0xFF0F0C29),
              ],
              stops: [0.0, 0.3, 0.7, 1.0],
            ),
          ),
        ),

        // Animated stars
        AnimatedBuilder(
          animation: _starsController,
          builder: (context, child) {
            return CustomPaint(
              size: Size.infinite,
              painter: StarFieldPainter(
                animation: _starsController.value,
              ),
            );
          },
        ),

        // Nebula clouds
        AnimatedBuilder(
          animation: _nebulaController,
          builder: (context, child) {
            return CustomPaint(
              size: Size.infinite,
              painter: NebulaPainter(
                animation: _nebulaController.value,
              ),
            );
          },
        ),

        // Floating planets
        AnimatedBuilder(
          animation: _planetController,
          builder: (context, child) {
            return CustomPaint(
              size: Size.infinite,
              painter: PlanetPainter(
                animation: _planetController.value,
              ),
            );
          },
        ),

        // Content overlay
        widget.child,
      ],
    );
  }
}

class StarFieldPainter extends CustomPainter {
  final double animation;

  StarFieldPainter({required this.animation});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..style = PaintingStyle.fill;

    // Generate consistent stars
    final random = math.Random(42); // Fixed seed for consistent stars
    
    for (int i = 0; i < 100; i++) {
      final x = random.nextDouble() * size.width;
      final y = random.nextDouble() * size.height;
      final starSize = random.nextDouble() * 2 + 0.5;
      
      // Twinkling effect
      final twinkle = math.sin(animation * 2 * math.pi + i) * 0.5 + 0.5;
      final alpha = 0.3 + twinkle * 0.7;
      
      paint.color = Colors.white.withValues(alpha: alpha);
      canvas.drawCircle(Offset(x, y), starSize, paint);
    }

    // Shooting stars
    for (int i = 0; i < 3; i++) {
      final progress = (animation + i * 0.3) % 1.0;
      final startX = -100 + progress * (size.width + 200);
      final startY = size.height * 0.2 + i * size.height * 0.3;
      
      if (progress > 0.1 && progress < 0.9) {
        final gradient = LinearGradient(
          colors: [
            Colors.white.withValues(alpha: 0.8),
            Colors.blue.withValues(alpha: 0.4),
            Colors.transparent,
          ],
        );
        
        final rect = Rect.fromLTWH(startX, startY, 60, 2);
        paint.shader = gradient.createShader(rect);
        canvas.drawRRect(
          RRect.fromRectAndRadius(rect, const Radius.circular(1)),
          paint,
        );
      }
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

class NebulaPainter extends CustomPainter {
  final double animation;

  NebulaPainter({required this.animation});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..style = PaintingStyle.fill;

    // Purple nebula
    final purpleGradient = RadialGradient(
      center: Alignment.topRight,
      radius: 1.5,
      colors: [
        const Color(0xFF6C5CE7).withValues(alpha: 0.3),
        const Color(0xFF2D3436).withValues(alpha: 0.1),
        Colors.transparent,
      ],
    );

    final purpleRect = Rect.fromLTWH(
      size.width * 0.6 + math.sin(animation * 2 * math.pi) * 50,
      -size.height * 0.2 + math.cos(animation * 1.5 * math.pi) * 30,
      size.width * 0.8,
      size.height * 0.6,
    );

    paint.shader = purpleGradient.createShader(purpleRect);
    canvas.drawOval(purpleRect, paint);

    // Blue nebula
    final blueGradient = RadialGradient(
      center: Alignment.bottomLeft,
      radius: 1.2,
      colors: [
        const Color(0xFF4ECDC4).withValues(alpha: 0.2),
        const Color(0xFF44A08D).withValues(alpha: 0.1),
        Colors.transparent,
      ],
    );

    final blueRect = Rect.fromLTWH(
      -size.width * 0.3 + math.cos(animation * 1.8 * math.pi) * 40,
      size.height * 0.4 + math.sin(animation * 2.2 * math.pi) * 60,
      size.width * 0.7,
      size.height * 0.8,
    );

    paint.shader = blueGradient.createShader(blueRect);
    canvas.drawOval(blueRect, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

class PlanetPainter extends CustomPainter {
  final double animation;

  PlanetPainter({required this.animation});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..style = PaintingStyle.fill;

    // Large planet
    final planetX = size.width * 0.8 + math.sin(animation * 2 * math.pi) * 20;
    final planetY = size.height * 0.3 + math.cos(animation * 1.5 * math.pi) * 15;

    final planetGradient = RadialGradient(
      colors: [
        const Color(0xFFFFD93D).withValues(alpha: 0.6),
        const Color(0xFFFF6B6B).withValues(alpha: 0.4),
        const Color(0xFF6C5CE7).withValues(alpha: 0.2),
      ],
    );

    final planetRect = Rect.fromCircle(
      center: Offset(planetX, planetY),
      radius: 40,
    );

    paint.shader = planetGradient.createShader(planetRect);
    canvas.drawCircle(Offset(planetX, planetY), 40, paint);

    // Planet rings
    paint.shader = null;
    paint.color = Colors.white.withValues(alpha: 0.3);
    paint.style = PaintingStyle.stroke;
    paint.strokeWidth = 2;

    canvas.save();
    canvas.translate(planetX, planetY);
    canvas.rotate(animation * 2 * math.pi);
    canvas.drawOval(const Rect.fromLTRB(-60, -20, 60, 20), paint);
    canvas.restore();

    // Small moon
    final moonX = planetX + math.cos(animation * 4 * math.pi) * 80;
    final moonY = planetY + math.sin(animation * 4 * math.pi) * 30;

    paint.style = PaintingStyle.fill;
    paint.color = Colors.white.withValues(alpha: 0.4);
    canvas.drawCircle(Offset(moonX, moonY), 8, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
