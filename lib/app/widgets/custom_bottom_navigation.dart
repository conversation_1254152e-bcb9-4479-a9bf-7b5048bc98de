import 'package:flutter/material.dart';
import '../screens/home_screen.dart';
import '../screens/dream_records_screen.dart';
import '../screens/statistics_screen.dart';
import '../screens/blog_screen.dart';

class CustomBottomNavigation extends StatelessWidget {
  final int currentIndex;
  final BuildContext context;

  const CustomBottomNavigation({
    super.key,
    required this.currentIndex,
    required this.context,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 90,
      decoration: const BoxDecoration(
        color: Colors.black,
        border: Border(
          top: BorderSide(
            color: Colors.white12,
            width: 0.5,
          ),
        ),
      ),
      child: SafeArea(
        top: false,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            _buildBottomNavItem(0, 'assets/images/dreamdeus.png'),
            _buildBottomNavItem(1, 'assets/images/feather.png'),
            _buildBottomNavItem(2, 'assets/images/rainbow.png'),
            _buildBottomNavItem(3, 'assets/images/triangle.png'),
          ],
        ),
      ),
    );
  }

  Widget _buildBottomNavItem(int index, String iconPath) {
    final isSelected = currentIndex == index;
    return GestureDetector(
      onTap: () => _onBottomNavTap(index),
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 20),
        child: Image.asset(
          iconPath,
          height: 32,
          width: 32,
          color: isSelected ? const Color(0xFF6C5CE7) : Colors.white54,
        ),
      ),
    );
  }

  void _onBottomNavTap(int index) {
    // Eğer zaten bulunduğumuz sayfaysa hiçbir şey yapma
    if (index == currentIndex) return;

    switch (index) {
      case 0:
        // Home
        _navigateToHome();
        break;
      case 1:
        // Dream Records
        _navigateToDreamRecords();
        break;
      case 2:
        // Statistics
        _navigateToStatistics();
        break;
      case 3:
        // Blog
        _navigateToBlog();
        break;
    }
  }

  void _navigateToHome() {
    // Ana sayfaya git - tüm stack'i temizle
    Navigator.of(context).pushAndRemoveUntil(
      MaterialPageRoute(builder: (context) => const HomeScreen()),
      (route) => false,
    );
  }

  void _navigateToDreamRecords() {
    if (currentIndex == 0) {
      // Home'dan geliyorsak push yap
      Navigator.of(context).push(
        MaterialPageRoute(builder: (context) => const DreamRecordsScreen()),
      );
    } else {
      // Başka sayfadan geliyorsak replace yap
      Navigator.of(context).pushReplacement(
        MaterialPageRoute(builder: (context) => const DreamRecordsScreen()),
      );
    }
  }

  void _navigateToStatistics() {
    if (currentIndex == 0) {
      // Home'dan geliyorsak push yap
      Navigator.of(context).push(
        MaterialPageRoute(builder: (context) => const StatisticsScreen()),
      );
    } else {
      // Başka sayfadan geliyorsak replace yap
      Navigator.of(context).pushReplacement(
        MaterialPageRoute(builder: (context) => const StatisticsScreen()),
      );
    }
  }

  void _navigateToBlog() {
    if (currentIndex == 0) {
      // Home'dan geliyorsak push yap
      Navigator.of(context).push(
        MaterialPageRoute(builder: (context) => const BlogScreen()),
      );
    } else {
      // Başka sayfadan geliyorsak replace yap
      Navigator.of(context).pushReplacement(
        MaterialPageRoute(builder: (context) => const BlogScreen()),
      );
    }
  }
}
