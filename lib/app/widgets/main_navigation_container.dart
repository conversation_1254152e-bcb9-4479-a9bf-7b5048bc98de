import 'package:flutter/material.dart';
import '../screens/home_screen.dart';
import '../screens/dream_records_screen.dart';
import '../screens/statistics_screen.dart';
import '../screens/blog_screen.dart';

class MainNavigationContainer extends StatefulWidget {
  final int initialIndex;

  const MainNavigationContainer({
    super.key,
    this.initialIndex = 0,
  });

  @override
  State<MainNavigationContainer> createState() => _MainNavigationContainerState();
}

class _MainNavigationContainerState extends State<MainNavigationContainer> {
  late PageController _pageController;
  int _currentIndex = 0;

  @override
  void initState() {
    super.initState();
    _currentIndex = widget.initialIndex;
    _pageController = PageController(initialPage: _currentIndex);
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  void _onBottomNavTap(int index) {
    if (index == _currentIndex) return;
    
    setState(() {
      _currentIndex = index;
    });
    
    // PageView'i animasyonsuz olarak değiştir
    _pageController.jumpToPage(index);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: PageView(
        controller: _pageController,
        physics: const NeverScrollableScrollPhysics(), // Swipe'ı devre dışı bırak
        onPageChanged: (index) {
          setState(() {
            _currentIndex = index;
          });
        },
        children: const [
          HomeScreenContent(), // Home screen içeriği
          DreamRecordsScreenContent(), // Dream records içeriği
          StatisticsScreenContent(), // Statistics içeriği
          BlogScreenContent(), // Blog içeriği
        ],
      ),
      bottomNavigationBar: _buildBottomNavigationBar(),
    );
  }

  Widget _buildBottomNavigationBar() {
    return Container(
      height: 90,
      decoration: const BoxDecoration(
        color: Colors.black,
        border: Border(
          top: BorderSide(
            color: Colors.white12,
            width: 0.5,
          ),
        ),
      ),
      child: SafeArea(
        top: false,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            _buildBottomNavItem(0, 'assets/images/dreamdeus.png'),
            _buildBottomNavItem(1, 'assets/images/feather.png'),
            _buildBottomNavItem(2, 'assets/images/rainbow.png'),
            _buildBottomNavItem(3, 'assets/images/triangle.png'),
          ],
        ),
      ),
    );
  }

  Widget _buildBottomNavItem(int index, String iconPath) {
    final isSelected = _currentIndex == index;
    return GestureDetector(
      onTap: () => _onBottomNavTap(index),
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 20),
        child: Image.asset(
          iconPath,
          height: 32,
          width: 32,
          color: isSelected ? const Color(0xFF6C5CE7) : Colors.white54,
        ),
      ),
    );
  }
}

// Content wrapper'ları - mevcut screen'lerin içeriklerini alacak
class HomeScreenContent extends StatelessWidget {
  const HomeScreenContent({super.key});

  @override
  Widget build(BuildContext context) {
    return const HomeScreen();
  }
}

class DreamRecordsScreenContent extends StatelessWidget {
  const DreamRecordsScreenContent({super.key});

  @override
  Widget build(BuildContext context) {
    return const DreamRecordsScreen();
  }
}

class StatisticsScreenContent extends StatelessWidget {
  const StatisticsScreenContent({super.key});

  @override
  Widget build(BuildContext context) {
    return const StatisticsScreen();
  }
}

class BlogScreenContent extends StatelessWidget {
  const BlogScreenContent({super.key});

  @override
  Widget build(BuildContext context) {
    return const BlogScreen();
  }
}
