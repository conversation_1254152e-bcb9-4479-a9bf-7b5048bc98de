import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart' as latlong;
import 'dart:math' as math;
import 'dart:ui' as ui;
import '../../models/dream_match.dart';

class MapLocation {
  final DreamMatch match;
  final String country;
  final Map<String, dynamic> userProfile;

  MapLocation({
    required this.match,
    required this.country,
    required this.userProfile,
  });
}



class InteractiveWorldMapWidget extends StatefulWidget {
  final List<DreamMatch> matches;

  const InteractiveWorldMapWidget({
    super.key,
    required this.matches,
  });

  @override
  State<InteractiveWorldMapWidget> createState() => _InteractiveWorldMapWidgetState();
}

class _InteractiveWorldMapWidgetState extends State<InteractiveWorldMapWidget>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late AnimationController _fadeController;
  late AnimationController _rotationController;
  late Animation<double> _pulseAnimation;
  late Animation<double> _fadeAnimation;

  final List<MapLocation> _locations = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _loadUserLocations();
  }

  void _setupAnimations() {
    _pulseController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    )..repeat(reverse: true);

    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _rotationController = AnimationController(
      duration: const Duration(seconds: 30),
      vsync: this,
    )..repeat();

    _pulseAnimation = Tween<double>(begin: 0.8, end: 1.2).animate(
      CurvedAnimation(parent: _pulseController, curve: Curves.easeInOut),
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fadeController, curve: Curves.easeIn),
    );

    _fadeController.forward();
  }

  Future<void> _loadUserLocations() async {
    final locations = <MapLocation>[];
    final processedUsers = <String>{};
    
    for (final match in widget.matches) {
      // Skip if we already processed this user
      if (processedUsers.contains(match.userId)) continue;
      
      try {
        final userDoc = await FirebaseFirestore.instance
            .collection('users')
            .doc(match.userId)
            .get();
        
        if (userDoc.exists) {
          final userData = userDoc.data()!;
          final country = userData['country'] ?? 'Unknown';
          
          final location = MapLocation(
            match: match,
            country: country,
            userProfile: userData,
          );
          
          locations.add(location);
          processedUsers.add(match.userId);
        }
      } catch (e) {
        print('Error loading user location for ${match.userId}: $e');
      }
    }
    
    if (mounted) {
      setState(() {
        _locations.clear();
        _locations.addAll(locations);
        _isLoading = false;
      });
    }
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _fadeController.dispose();
    _rotationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 300,
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            const Color(0xFF6C5CE7).withValues(alpha: 0.8),
            const Color(0xFF2D3436).withValues(alpha: 0.9),
          ],
        ),
        boxShadow: [
          BoxShadow(
            color: const Color(0xFF6C5CE7).withValues(alpha: 0.3),
            blurRadius: 20,
            spreadRadius: 5,
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(20),
        child: Column(
          children: [
            // Header
            Container(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: const Color(0xFF6C5CE7).withValues(alpha: 0.3),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: const Icon(
                      Icons.public,
                      color: Colors.white,
                      size: 20,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Global Dream Connections',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        '${_getUniqueCountries()} countries • ${widget.matches.length} dreamers',
                        style: TextStyle(
                          color: Colors.white.withValues(alpha: 0.7),
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            // Map
            Expanded(
              child: Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      const Color(0xFF1a1a2e).withValues(alpha: 0.9),
                      const Color(0xFF16213e).withValues(alpha: 0.95),
                    ],
                  ),
                ),
                child: _isLoading 
                    ? _buildLoadingOverlay()
                    : _buildMap(),
              ),
            ),
            // Footer
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              child: Row(
                children: [
                  Icon(
                    Icons.pan_tool,
                    color: Colors.white.withValues(alpha: 0.6),
                    size: 16,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'Pinch to zoom • Tap dots for details',
                    style: TextStyle(
                      color: Colors.white.withValues(alpha: 0.6),
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMap() {
    return FlutterMap(
      options: MapOptions(
        initialCenter: latlong.LatLng(20, 0), // Center of world
        initialZoom: 2.0,
        minZoom: 1.0,
        maxZoom: 6.0,
      ),
      children: [
        // Dark themed world map
        TileLayer(
          urlTemplate: 'https://cartodb-basemaps-{s}.global.ssl.fastly.net/dark_all/{z}/{x}/{y}.png',
          subdomains: const ['a', 'b', 'c', 'd'],
          userAgentPackageName: 'com.example.thedreamdeus',
        ),
        // Dream location markers
        MarkerLayer(
          markers: _buildMarkers(),
        ),
      ],
    );
  }

  List<Marker> _buildMarkers() {
    return _locations.map((location) {
      final coords = _getCountryCoordinates(location.country);

      return Marker(
        point: latlong.LatLng(coords['lat']!, coords['lon']!),
        width: 40,
        height: 40,
        child: GestureDetector(
          onTap: () => _showLocationDetails([location]),
          child: AnimatedBuilder(
            animation: _pulseAnimation,
            builder: (context, child) {
              return Transform.scale(
                scale: _pulseAnimation.value,
                child: Container(
                  width: 16,
                  height: 16,
                  decoration: BoxDecoration(
                    color: _getColorForSimilarity(location.match.similarityScore),
                    shape: BoxShape.circle,
                    boxShadow: [
                      BoxShadow(
                        color: _getColorForSimilarity(location.match.similarityScore).withValues(alpha: 0.6),
                        blurRadius: 12,
                        spreadRadius: 4,
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
        ),
      );
    }).toList();
  }

  String _cleanCountryName(String country) {
    // Remove flag emojis and extra spaces
    String cleaned = country.replaceAll(RegExp(r'[\u{1F1E6}-\u{1F1FF}]', unicode: true), ''); // Remove flag emojis
    cleaned = cleaned.replaceAll(RegExp(r'\s+'), ' '); // Replace multiple spaces with single space
    cleaned = cleaned.trim(); // Remove leading/trailing spaces
    return cleaned;
  }

  Map<String, double> _getCountryCoordinates(String country) {
    // Clean the country name first
    final cleanedCountry = _cleanCountryName(country);
    print('DEBUG: Original country: "$country", Cleaned: "$cleanedCountry"');

    // Complete coordinates mapping for all 195 countries
    final countryCoords = {
      // A
      'Afghanistan': {'lat': 33.9, 'lon': 67.7},
      'Albania': {'lat': 41.2, 'lon': 20.2},
      'Algeria': {'lat': 28.0, 'lon': 1.7},
      'Andorra': {'lat': 42.5, 'lon': 1.6},
      'Angola': {'lat': -11.2, 'lon': 17.9},
      'Antigua and Barbuda': {'lat': 17.1, 'lon': -61.8},
      'Argentina': {'lat': -38.4, 'lon': -63.6},
      'Armenia': {'lat': 40.1, 'lon': 45.0},
      'Australia': {'lat': -25.3, 'lon': 133.8},
      'Austria': {'lat': 47.5, 'lon': 14.6},
      'Azerbaijan': {'lat': 40.1, 'lon': 47.6},

      // B
      'Bahamas': {'lat': 25.0, 'lon': -77.4},
      'Bahrain': {'lat': 25.9, 'lon': 50.6},
      'Bangladesh': {'lat': 23.7, 'lon': 90.4},
      'Barbados': {'lat': 13.2, 'lon': -59.5},
      'Belarus': {'lat': 53.7, 'lon': 27.9},
      'Belgium': {'lat': 50.5, 'lon': 4.5},
      'Belize': {'lat': 17.2, 'lon': -88.5},
      'Benin': {'lat': 9.3, 'lon': 2.3},
      'Bhutan': {'lat': 27.5, 'lon': 90.4},
      'Bolivia': {'lat': -16.3, 'lon': -63.6},
      'Bosnia and Herzegovina': {'lat': 43.9, 'lon': 17.7},
      'Botswana': {'lat': -22.3, 'lon': 24.7},
      'Brazil': {'lat': -14.2, 'lon': -51.9},
      'Brunei': {'lat': 4.5, 'lon': 114.7},
      'Bulgaria': {'lat': 42.7, 'lon': 25.5},
      'Burkina Faso': {'lat': 12.2, 'lon': -1.6},
      'Burundi': {'lat': -3.4, 'lon': 29.9},

      // C
      'Cabo Verde': {'lat': 16.0, 'lon': -24.0},
      'Cambodia': {'lat': 12.6, 'lon': 104.9},
      'Cameroon': {'lat': 7.4, 'lon': 12.4},
      'Canada': {'lat': 56.1, 'lon': -106.3},
      'Central African Republic': {'lat': 6.6, 'lon': 20.9},
      'Chad': {'lat': 15.5, 'lon': 18.7},
      'Chile': {'lat': -35.7, 'lon': -71.5},
      'China': {'lat': 35.9, 'lon': 104.2},
      'Colombia': {'lat': 4.6, 'lon': -74.3},
      'Comoros': {'lat': -11.9, 'lon': 43.9},
      'Congo': {'lat': -0.2, 'lon': 15.8},
      'Costa Rica': {'lat': 9.7, 'lon': -83.8},
      'Croatia': {'lat': 45.1, 'lon': 15.2},
      'Cuba': {'lat': 21.5, 'lon': -77.8},
      'Cyprus': {'lat': 35.1, 'lon': 33.4},
      'Czech Republic': {'lat': 49.8, 'lon': 15.5},
      'Czechia': {'lat': 49.8, 'lon': 15.5},

      // D
      'Democratic Republic of the Congo': {'lat': -4.0, 'lon': 21.8},
      'Denmark': {'lat': 56.3, 'lon': 9.5},
      'Djibouti': {'lat': 11.8, 'lon': 42.6},
      'Dominica': {'lat': 15.4, 'lon': -61.4},
      'Dominican Republic': {'lat': 18.7, 'lon': -70.2},

      // E
      'Ecuador': {'lat': -1.8, 'lon': -78.2},
      'Egypt': {'lat': 26.8, 'lon': 30.8},
      'El Salvador': {'lat': 13.8, 'lon': -88.9},
      'Equatorial Guinea': {'lat': 1.7, 'lon': 10.3},
      'Eritrea': {'lat': 15.2, 'lon': 39.8},
      'Estonia': {'lat': 58.6, 'lon': 25.0},
      'Eswatini': {'lat': -26.5, 'lon': 31.5},
      'Ethiopia': {'lat': 9.1, 'lon': 40.5},

      // F
      'Fiji': {'lat': -16.6, 'lon': 179.4},
      'Finland': {'lat': 61.9, 'lon': 25.7},
      'France': {'lat': 46.6, 'lon': 2.2},

      // G
      'Gabon': {'lat': -0.8, 'lon': 11.6},
      'Gambia': {'lat': 13.4, 'lon': -15.3},
      'Georgia': {'lat': 42.3, 'lon': 43.4},
      'Germany': {'lat': 51.2, 'lon': 10.4},
      'Ghana': {'lat': 7.9, 'lon': -1.0},
      'Greece': {'lat': 39.1, 'lon': 21.8},
      'Grenada': {'lat': 12.3, 'lon': -61.6},
      'Guatemala': {'lat': 15.8, 'lon': -90.2},
      'Guinea': {'lat': 9.9, 'lon': -9.7},
      'Guinea-Bissau': {'lat': 11.8, 'lon': -15.2},
      'Guyana': {'lat': 4.9, 'lon': -58.9},

      // H
      'Haiti': {'lat': 18.9, 'lon': -72.3},
      'Holy See': {'lat': 41.9, 'lon': 12.5},
      'Honduras': {'lat': 15.2, 'lon': -86.2},
      'Hungary': {'lat': 47.2, 'lon': 19.5},

      // I
      'Iceland': {'lat': 64.9, 'lon': -19.0},
      'India': {'lat': 20.6, 'lon': 78.9},
      'Indonesia': {'lat': -0.8, 'lon': 113.9},
      'Iran': {'lat': 32.4, 'lon': 53.7},
      'Iraq': {'lat': 33.2, 'lon': 43.7},
      'Ireland': {'lat': 53.4, 'lon': -8.2},
      'Israel': {'lat': 31.0, 'lon': 34.9},
      'Italy': {'lat': 41.9, 'lon': 12.6},

      // J
      'Jamaica': {'lat': 18.1, 'lon': -77.3},
      'Japan': {'lat': 36.2, 'lon': 138.3},
      'Jordan': {'lat': 30.6, 'lon': 36.2},

      // K
      'Kazakhstan': {'lat': 48.0, 'lon': 66.9},
      'Kenya': {'lat': -0.0, 'lon': 37.9},
      'Kiribati': {'lat': -3.4, 'lon': -168.7},
      'Kuwait': {'lat': 29.3, 'lon': 47.5},
      'Kyrgyzstan': {'lat': 41.2, 'lon': 74.8},

      // L
      'Laos': {'lat': 19.9, 'lon': 102.5},
      'Latvia': {'lat': 56.9, 'lon': 24.6},
      'Lebanon': {'lat': 33.9, 'lon': 35.9},
      'Lesotho': {'lat': -29.6, 'lon': 28.2},
      'Liberia': {'lat': 6.4, 'lon': -9.4},
      'Libya': {'lat': 26.3, 'lon': 17.2},
      'Liechtenstein': {'lat': 47.2, 'lon': 9.5},
      'Lithuania': {'lat': 55.2, 'lon': 23.9},
      'Luxembourg': {'lat': 49.8, 'lon': 6.1},

      // M
      'Madagascar': {'lat': -18.8, 'lon': 47.0},
      'Malawi': {'lat': -13.3, 'lon': 34.3},
      'Malaysia': {'lat': 4.2, 'lon': 101.9},
      'Maldives': {'lat': 3.2, 'lon': 73.2},
      'Mali': {'lat': 17.6, 'lon': -4.0},
      'Malta': {'lat': 35.9, 'lon': 14.4},
      'Marshall Islands': {'lat': 7.1, 'lon': 171.2},
      'Mauritania': {'lat': 21.0, 'lon': -10.9},
      'Mauritius': {'lat': -20.3, 'lon': 57.6},
      'Mexico': {'lat': 23.6, 'lon': -102.6},
      'Micronesia': {'lat': 7.4, 'lon': 150.5},
      'Moldova': {'lat': 47.4, 'lon': 28.4},
      'Monaco': {'lat': 43.7, 'lon': 7.4},
      'Mongolia': {'lat': 46.9, 'lon': 103.8},
      'Montenegro': {'lat': 42.7, 'lon': 19.4},
      'Morocco': {'lat': 31.8, 'lon': -7.1},
      'Mozambique': {'lat': -18.7, 'lon': 35.5},
      'Myanmar': {'lat': 21.9, 'lon': 95.9},

      // N
      'Namibia': {'lat': -22.6, 'lon': 18.5},
      'Nauru': {'lat': -0.5, 'lon': 166.9},
      'Nepal': {'lat': 28.4, 'lon': 84.1},
      'Netherlands': {'lat': 52.1, 'lon': 5.3},
      'New Zealand': {'lat': -40.9, 'lon': 174.9},
      'Nicaragua': {'lat': 12.9, 'lon': -85.2},
      'Niger': {'lat': 17.6, 'lon': 8.1},
      'Nigeria': {'lat': 9.1, 'lon': 8.7},
      'North Korea': {'lat': 40.3, 'lon': 127.5},
      'North Macedonia': {'lat': 41.6, 'lon': 21.7},
      'Norway': {'lat': 60.5, 'lon': 8.5},

      // O
      'Oman': {'lat': 21.5, 'lon': 55.9},

      // P
      'Pakistan': {'lat': 30.4, 'lon': 69.3},
      'Palau': {'lat': 7.5, 'lon': 134.6},
      'Palestine': {'lat': 31.9, 'lon': 35.2},
      'Panama': {'lat': 8.5, 'lon': -80.8},
      'Papua New Guinea': {'lat': -6.3, 'lon': 143.9},
      'Paraguay': {'lat': -23.4, 'lon': -58.4},
      'Peru': {'lat': -9.2, 'lon': -75.0},
      'Philippines': {'lat': 12.9, 'lon': 121.8},
      'Poland': {'lat': 51.9, 'lon': 19.1},
      'Portugal': {'lat': 39.4, 'lon': -8.2},

      // Q
      'Qatar': {'lat': 25.4, 'lon': 51.2},

      // R
      'Romania': {'lat': 45.9, 'lon': 24.9},
      'Russia': {'lat': 61.5, 'lon': 105.3},
      'Rwanda': {'lat': -1.9, 'lon': 29.9},

      // S
      'Saint Kitts and Nevis': {'lat': 17.4, 'lon': -62.8},
      'Saint Lucia': {'lat': 13.9, 'lon': -60.9},
      'Saint Vincent and the Grenadines': {'lat': 12.9, 'lon': -61.3},
      'Samoa': {'lat': -13.8, 'lon': -171.8},
      'San Marino': {'lat': 43.9, 'lon': 12.5},
      'Sao Tome and Principe': {'lat': 0.2, 'lon': 6.6},
      'Saudi Arabia': {'lat': 23.9, 'lon': 45.1},
      'Senegal': {'lat': 14.5, 'lon': -14.5},
      'Serbia': {'lat': 44.0, 'lon': 21.0},
      'Seychelles': {'lat': -4.7, 'lon': 55.5},
      'Sierra Leone': {'lat': 8.5, 'lon': -11.8},
      'Singapore': {'lat': 1.4, 'lon': 103.8},
      'Slovakia': {'lat': 48.7, 'lon': 19.7},
      'Slovenia': {'lat': 46.2, 'lon': 14.9},
      'Solomon Islands': {'lat': -9.6, 'lon': 160.2},
      'Somalia': {'lat': 5.2, 'lon': 46.2},
      'South Africa': {'lat': -30.6, 'lon': 22.9},
      'South Korea': {'lat': 35.9, 'lon': 127.8},
      'South Sudan': {'lat': 6.9, 'lon': 31.3},
      'Spain': {'lat': 40.5, 'lon': -3.7},
      'Sri Lanka': {'lat': 7.9, 'lon': 80.8},
      'Sudan': {'lat': 12.9, 'lon': 30.2},
      'Suriname': {'lat': 3.9, 'lon': -56.0},
      'Sweden': {'lat': 60.1, 'lon': 18.6},
      'Switzerland': {'lat': 46.8, 'lon': 8.2},
      'Syria': {'lat': 34.8, 'lon': 38.9},

      // T
      'Tajikistan': {'lat': 38.9, 'lon': 71.3},
      'Tanzania': {'lat': -6.4, 'lon': 34.9},
      'Thailand': {'lat': 15.9, 'lon': 100.9},
      'Timor-Leste': {'lat': -8.9, 'lon': 125.7},
      'Togo': {'lat': 8.6, 'lon': 0.8},
      'Tonga': {'lat': -21.2, 'lon': -175.2},
      'Trinidad and Tobago': {'lat': 10.7, 'lon': -61.2},
      'Tunisia': {'lat': 33.9, 'lon': 9.5},
      'Turkey': {'lat': 39.0, 'lon': 35.0},
      'Türkiye': {'lat': 39.0, 'lon': 35.0},
      'Turkmenistan': {'lat': 38.9, 'lon': 59.6},
      'Tuvalu': {'lat': -7.1, 'lon': 177.6},

      // U
      'Uganda': {'lat': 1.4, 'lon': 32.3},
      'Ukraine': {'lat': 48.4, 'lon': 31.2},
      'United Arab Emirates': {'lat': 23.4, 'lon': 53.8},
      'United Kingdom': {'lat': 55.4, 'lon': -3.4},
      'United States': {'lat': 39.8, 'lon': -98.5},
      'Uruguay': {'lat': -32.5, 'lon': -55.8},
      'Uzbekistan': {'lat': 41.4, 'lon': 64.6},

      // V
      'Vanuatu': {'lat': -15.4, 'lon': 166.9},
      'Venezuela': {'lat': 6.4, 'lon': -66.6},
      'Vietnam': {'lat': 14.1, 'lon': 108.3},

      // Y
      'Yemen': {'lat': 15.6, 'lon': 48.5},

      // Z
      'Zambia': {'lat': -13.1, 'lon': 27.8},
      'Zimbabwe': {'lat': -19.0, 'lon': 29.2},
    };

    final coords = countryCoords[cleanedCountry];
    if (coords == null) {
      print('DEBUG: Country "$cleanedCountry" not found in coordinates map, using default (0,0)');
      return {'lat': 0.0, 'lon': 0.0}; // Default center
    }
    print('DEBUG: Found coordinates for "$cleanedCountry": lat=${coords['lat']}, lon=${coords['lon']}');
    return coords;
  }

  Color _getColorForSimilarity(double similarity) {
    final percentage = (similarity * 100).round();
    if (percentage >= 90) return const Color(0xFF00FF88); // Bright green
    if (percentage >= 80) return const Color(0xFF00FFFF); // Cyan
    if (percentage >= 70) return const Color(0xFF0080FF); // Blue
    return const Color(0xFFFF6B6B); // Red
  }

  int _getUniqueCountries() {
    return _locations.map((l) => l.country).toSet().length;
  }

  Widget _buildLoadingOverlay() {
    return Container(
      color: Colors.black.withValues(alpha: 0.3),
      child: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF6C5CE7)),
            ),
            SizedBox(height: 16),
            Text(
              'Loading dream connections...',
              style: TextStyle(
                color: Colors.white,
                fontSize: 14,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showLocationDetails(List<MapLocation> locations) {
    final location = locations.first;
    final profile = location.userProfile;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: const Color(0xFF2D3436),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        title: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: _getColorForSimilarity(location.match.similarityScore).withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                Icons.location_on,
                color: _getColorForSimilarity(location.match.similarityScore),
                size: 20,
              ),
            ),
            const SizedBox(width: 12),
            const Expanded(
              child: Text(
                'Dream Match',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildDetailRow('Location', location.country),
            _buildDetailRow('Similarity', '${(location.match.similarityScore * 100).round()}%'),
            _buildDetailRow('Age Range', _getAgeRange(profile['age'])),
            _buildDetailRow('Gender', profile['gender'] ?? 'Not specified'),
            _buildDetailRow('Education', profile['educationLevel'] ?? 'Not specified'),
            const SizedBox(height: 16),
            Text(
              'Dream: ${location.match.title}',
              style: TextStyle(
                color: Colors.white.withValues(alpha: 0.8),
                fontSize: 14,
                fontStyle: FontStyle.italic,
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text(
              'Close',
              style: TextStyle(color: Color(0xFF6C5CE7)),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: TextStyle(
                color: Colors.white.withValues(alpha: 0.7),
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 14,
              ),
            ),
          ),
        ],
      ),
    );
  }

  String _getAgeRange(dynamic age) {
    if (age == null) return 'Not specified';
    final ageInt = age is int ? age : int.tryParse(age.toString()) ?? 0;
    if (ageInt < 18) return 'Under 18';
    if (ageInt < 25) return '18-24';
    if (ageInt < 35) return '25-34';
    if (ageInt < 45) return '35-44';
    if (ageInt < 55) return '45-54';
    return '55+';
  }
}


